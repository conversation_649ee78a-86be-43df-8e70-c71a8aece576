#!/usr/bin/env python3
"""
优化版启动脚本
针对移动端访问进行性能优化
"""

import os
import sys
from flask import Flask
from app import app

def optimize_for_mobile():
    """移动端优化配置"""
    # 禁用调试模式以提高性能
    app.config['DEBUG'] = False
    
    # 启用模板缓存
    app.config['TEMPLATES_AUTO_RELOAD'] = False
    
    # 设置最大内容长度
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB
    
    # 优化JSON响应
    app.config['JSONIFY_PRETTYPRINT_REGULAR'] = False
    
    print("移动端优化配置已启用")

def check_static_files():
    """检查静态文件是否存在"""
    required_files = [
        'static/css/bootstrap.min.css',
        'static/css/bootstrap-icons.css',
        'static/js/bootstrap.bundle.min.js',
        'static/js/html5-qrcode.min.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("警告：以下静态文件缺失，可能影响性能：")
        for file_path in missing_files:
            print(f"  - {file_path}")
        print("建议运行 python download_assets.py 下载缺失文件")
        return False
    
    print("所有静态文件检查完成")
    return True

def main():
    """主函数"""
    print("=== 称重系统优化启动器 ===")
    print("针对移动端访问进行性能优化")
    print()
    
    # 检查静态文件
    check_static_files()
    
    # 应用移动端优化
    optimize_for_mobile()
    
    # 检查SSL证书
    ssl_cert = 'ssl/server.crt'
    ssl_key = 'ssl/server.key'
    
    if os.path.exists(ssl_cert) and os.path.exists(ssl_key):
        print("使用HTTPS模式启动（推荐用于移动端）...")
        print("访问地址:")
        print("  - 本地: https://localhost:5050")
        print("  - 内网: https://您的IP:5050")
        print()
        print("移动端优化特性:")
        print("  ✓ 静态资源本地化")
        print("  ✓ 响应式设计优化")
        print("  ✓ 摄像头性能调优")
        print("  ✓ 网络自适应配置")
        print("  ✓ 缓存策略优化")
        print()
        print("按 Ctrl+C 停止服务器")
        print("=" * 40)
        
        try:
            app.run(
                host='0.0.0.0', 
                port=5050, 
                ssl_context=(ssl_cert, ssl_key),
                threaded=True,  # 启用多线程
                debug=False     # 生产模式
            )
        except KeyboardInterrupt:
            print("\n服务器已停止")
    else:
        print("SSL证书不存在，使用HTTP模式...")
        print("警告: HTTP模式下移动端摄像头功能受限")
        print("建议运行 python generate_ssl_cert.py 生成证书")
        print()
        
        try:
            app.run(
                host='0.0.0.0', 
                port=5050,
                threaded=True,
                debug=False
            )
        except KeyboardInterrupt:
            print("\n服务器已停止")

if __name__ == "__main__":
    main()
