/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.4rem;
}

/* 卡片样式 */
.card {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: none;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 20px;
}

.card-header {
    font-weight: 600;
    padding: 12px 20px;
}

.card-body {
    padding: 20px;
}

/* 按钮样式 */
.btn {
    border-radius: 5px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* 表单样式 */
.form-label {
    font-weight: 500;
    margin-bottom: 6px;
}

.form-control {
    border-radius: 5px;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

.table td, .table th {
    padding: 12px 15px;
    vertical-align: middle;
}

/* 扫码页面样式 */
.scanner-container {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    position: relative;
}

#scanner-preview {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #0d6efd;
}

.product-info {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.weight-input {
    font-size: 1.2rem;
    font-weight: 500;
    padding: 12px 15px;
}

/* 摄像头和对焦控制样式 */
#camera-controls {
    max-width: 500px;
    margin: 0 auto;
}

#camera-controls .card {
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#camera-controls .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 10px 15px;
}

#camera-controls .card-header h6 {
    color: #495057;
    font-weight: 600;
}

#camera-select {
    border-radius: 5px;
    border: 1px solid #ced4da;
    transition: border-color 0.2s ease;
}

#camera-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.form-range {
    height: 6px;
}

.form-range::-webkit-slider-thumb {
    background-color: #0d6efd;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.form-range::-moz-range-thumb {
    background-color: #0d6efd;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

#focus-controls .btn-outline-primary {
    border-color: #0d6efd;
    color: #0d6efd;
}

#focus-controls .btn-outline-primary:hover {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: #fff;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }

    .card {
        margin-bottom: 15px;
    }

    .card-body {
        padding: 15px;
    }

    .table td, .table th {
        padding: 8px 10px;
        font-size: 14px;
    }

    .btn {
        padding: 10px 16px;
        font-size: 14px;
    }

    /* 扫描器移动端优化 */
    #scanner-preview {
        height: 250px !important;
    }

    /* 表单优化 - 防止iOS缩放 */
    .form-control {
        font-size: 16px;
    }

    /* 导航栏优化 */
    .navbar-brand {
        font-size: 1.2rem;
    }

    /* 按钮组优化 */
    .btn-group-vertical .btn {
        margin-bottom: 5px;
    }

    /* 产品信息卡片优化 */
    .product-info {
        padding: 15px;
        margin-top: 15px;
    }

    /* 重量输入框优化 */
    .weight-input {
        font-size: 1.1rem;
        padding: 10px 12px;
    }
}