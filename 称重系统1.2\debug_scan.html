<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扫码调试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <h2 class="text-center mb-4">
                    <i class="bi bi-bug me-2"></i>扫码调试页面
                </h2>
                
                <!-- 扫描器区域 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">二维码扫描器</h5>
                    </div>
                    <div class="card-body">
                        <div id="scanner-preview" style="width: 100%; height: 300px; border: 2px solid #dee2e6; border-radius: 8px;"></div>
                        <div class="text-center mt-3">
                            <button id="start-scan" class="btn btn-primary me-2">
                                <i class="bi bi-camera-video me-1"></i>启动扫描
                            </button>
                            <button id="stop-scan" class="btn btn-secondary" disabled>
                                <i class="bi bi-stop-circle me-1"></i>停止扫描
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 手动测试区域 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">手动测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="input-group">
                            <input type="text" class="form-control" id="manual-input" placeholder="输入产品编号进行测试">
                            <button class="btn btn-outline-primary" id="manual-test">测试查询</button>
                        </div>
                    </div>
                </div>
                
                <!-- 结果显示区域 -->
                <div id="result-card" class="card mb-4" style="display: none;">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="bi bi-check-circle me-2"></i>查询结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="result-content"></div>
                    </div>
                </div>
                
                <!-- 调试日志 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-terminal me-2"></i>调试日志</h5>
                    </div>
                    <div class="card-body">
                        <div id="debug-log" style="background-color: #212529; color: #fff; padding: 15px; border-radius: 8px; height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.9rem;">
                            等待操作...
                        </div>
                        <button id="clear-log" class="btn btn-outline-secondary btn-sm mt-2">清空日志</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let html5QrCode = null;
        let scanning = false;

        const startBtn = document.getElementById('start-scan');
        const stopBtn = document.getElementById('stop-scan');
        const manualInput = document.getElementById('manual-input');
        const manualTestBtn = document.getElementById('manual-test');
        const resultCard = document.getElementById('result-card');
        const resultContent = document.getElementById('result-content');
        const debugLog = document.getElementById('debug-log');

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            console.log(logEntry);
            
            const logDiv = document.createElement('div');
            logDiv.textContent = logEntry;
            if (type === 'error') logDiv.style.color = '#ff6b6b';
            if (type === 'success') logDiv.style.color = '#51cf66';
            if (type === 'warning') logDiv.style.color = '#ffd43b';
            
            debugLog.appendChild(logDiv);
            debugLog.scrollTop = debugLog.scrollHeight;
        }

        // 清空日志
        document.getElementById('clear-log').addEventListener('click', () => {
            debugLog.innerHTML = '';
        });

        // 查询产品信息
        function fetchProductInfo(productId) {
            log(`🔍 开始查询产品ID: "${productId}"`);
            
            // 验证产品ID
            if (!productId || productId.trim() === '') {
                log('❌ 产品ID为空', 'error');
                showResult('错误', '产品编号不能为空', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('product_id', productId.trim());
            
            log(`📤 发送API请求到 /api/find_product`);
            log(`📤 请求参数: product_id = "${productId.trim()}"`);
            
            fetch('/api/find_product', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                log(`📥 API响应状态: ${response.status} ${response.statusText}`);
                if (!response.ok) {
                    throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                log(`📥 API返回数据:`, 'info');
                log(JSON.stringify(data, null, 2));
                
                if (data.success) {
                    log('✅ 查询成功', 'success');
                    showResult('查询成功', data.product, 'success');
                } else {
                    log(`❌ 查询失败: ${data.message}`, 'error');
                    showResult('查询失败', data.message, 'error');
                }
            })
            .catch(error => {
                log(`❌ 查询错误: ${error.message}`, 'error');
                showResult('系统错误', `查询过程中发生错误: ${error.message}`, 'error');
            });
        }

        // 显示结果
        function showResult(title, content, type) {
            const cardHeader = resultCard.querySelector('.card-header');
            
            if (type === 'success') {
                cardHeader.className = 'card-header bg-success text-white';
                cardHeader.innerHTML = `<h5 class="mb-0"><i class="bi bi-check-circle me-2"></i>${title}</h5>`;
                
                if (typeof content === 'object') {
                    // 显示产品信息
                    resultContent.innerHTML = `
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>产品编号:</strong> ${content.uid || '无'}</p>
                                <p><strong>产品代码:</strong> ${content.product_code || '无'}</p>
                                <p><strong>规格信息:</strong> ${content.spec_info || '无'}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>特殊要求:</strong> ${content.special_req || '无'}</p>
                                <p><strong>客户:</strong> ${content.customer || '无'}</p>
                                <p><strong>更新时间:</strong> ${content.update_time || '无'}</p>
                            </div>
                        </div>
                        <div class="mt-3">
                            <h6>完整数据:</h6>
                            <pre class="bg-light p-2 rounded">${JSON.stringify(content, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultContent.innerHTML = `<p>${content}</p>`;
                }
            } else {
                cardHeader.className = 'card-header bg-danger text-white';
                cardHeader.innerHTML = `<h5 class="mb-0"><i class="bi bi-x-circle me-2"></i>${title}</h5>`;
                resultContent.innerHTML = `<div class="alert alert-danger">${content}</div>`;
            }
            
            resultCard.style.display = 'block';
            resultCard.scrollIntoView({ behavior: 'smooth' });
        }

        // 启动扫描
        startBtn.addEventListener('click', () => {
            if (!html5QrCode) {
                html5QrCode = new Html5Qrcode("scanner-preview");
            }

            const config = {
                fps: 10,
                qrbox: { width: 250, height: 250 },
                aspectRatio: 1.0,
                formatsToSupport: [Html5QrcodeSupportedFormats.QR_CODE],
                videoConstraints: {
                    facingMode: "environment",
                    width: { ideal: 1280 },
                    height: { ideal: 720 },
                    focusMode: "continuous",
                    focusDistance: { ideal: 0.1 }
                }
            };

            html5QrCode.start(
                { facingMode: "environment" },
                config,
                (decodedText) => {
                    log(`🎉 扫描成功! 内容: "${decodedText}"`, 'success');
                    log(`内容类型: ${typeof decodedText}`, 'info');
                    log(`内容长度: ${decodedText ? decodedText.length : 0}`, 'info');
                    
                    // 停止扫描
                    stopScanning();
                    
                    // 查询产品信息
                    fetchProductInfo(decodedText);
                },
                (errorMessage) => {
                    // 扫描过程中的错误（通常是没有检测到二维码）
                    // console.log('扫描过程中:', errorMessage);
                }
            ).then(() => {
                log('📷 扫描器启动成功', 'success');
                scanning = true;
                startBtn.disabled = true;
                stopBtn.disabled = false;
            }).catch(error => {
                log(`❌ 扫描器启动失败: ${error.message}`, 'error');
            });
        });

        // 停止扫描
        function stopScanning() {
            if (html5QrCode && scanning) {
                html5QrCode.stop().then(() => {
                    log('📷 扫描器已停止');
                    scanning = false;
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                }).catch(error => {
                    log(`❌ 停止扫描器失败: ${error.message}`, 'error');
                });
            }
        }

        // 停止扫描按钮
        stopBtn.addEventListener('click', stopScanning);

        // 手动测试
        manualTestBtn.addEventListener('click', () => {
            const productId = manualInput.value.trim();
            if (productId) {
                fetchProductInfo(productId);
            } else {
                log('❌ 请输入产品编号', 'warning');
            }
        });

        // 回车键测试
        manualInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                manualTestBtn.click();
            }
        });

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 扫码调试页面已加载');
        });
    </script>
</body>
</html>
