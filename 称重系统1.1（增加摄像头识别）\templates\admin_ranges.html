<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编号段管理 - 称重管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap-icons.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">称重管理系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/scan">扫码称重</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin">管理后台</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>编号段管理</h2>
                    <a href="/admin" class="btn btn-secondary">返回管理首页</a>
                </div>
                
                <!-- 添加编号段表单 -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">添加编号段</h5>
                    </div>
                    <div class="card-body">
                        <form id="add-range-form">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="start_id" class="form-label">起始编号</label>
                                    <input type="text" class="form-control" id="start_id" name="start_id" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="end_id" class="form-label">结束编号</label>
                                    <input type="text" class="form-control" id="end_id" name="end_id" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="product_code" class="form-label">产品代码</label>
                                    <input type="text" class="form-control" id="product_code" name="product_code" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="spec_info" class="form-label">型号规格</label>
                                    <input type="text" class="form-control" id="spec_info" name="spec_info" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="special_req" class="form-label">特殊要求</label>
                                    <input type="text" class="form-control" id="special_req" name="special_req">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="customer" class="form-label">需求单位</label>
                                    <input type="text" class="form-control" id="customer" name="customer">
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">添加编号段</button>
                        </form>
                    </div>
                </div>
                
                <!-- 导入导出 -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">批量操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>导入编号段</h6>
                                <form id="import-form" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <input type="file" class="form-control" id="import-file" name="file" accept=".xlsx">
                                    </div>
                                    <button type="submit" class="btn btn-primary">导入</button>
                                </form>
                            </div>
                            <div class="col-md-6">
                                <h6>导出编号段</h6>
                                <button id="export-ranges" class="btn btn-primary">导出到Excel</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 编号段列表 -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">编号段列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>起始编号</th>
                                        <th>结束编号</th>
                                        <th>产品代码</th>
                                        <th>型号规格</th>
                                        <th>特殊要求</th>
                                        <th>需求单位</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for range in ranges %}
                                    <tr>
                                        <td>{{ range.start_id }}</td>
                                        <td>{{ range.end_id }}</td>
                                        <td>{{ range.product_code }}</td>
                                        <td>{{ range.spec_info }}</td>
                                        <td>{{ range.special_req }}</td>
                                        <td>{{ range.customer }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-danger delete-range" data-start="{{ range.start_id }}" data-end="{{ range.end_id }}">删除</button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                    {% if not ranges %}
                                    <tr>
                                        <td colspan="7" class="text-center">暂无编号段数据</td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script>
        // 添加编号段
        document.getElementById('add-range-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('/api/add_range', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('编号段添加成功');
                    window.location.reload();
                } else {
                    alert('添加失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('添加错误:', error);
                alert('添加过程中发生错误');
            });
        });
        
        // 删除编号段
        document.querySelectorAll('.delete-range').forEach(button => {
            button.addEventListener('click', function() {
                if (confirm('确定要删除这个编号段吗？')) {
                    const start_id = this.getAttribute('data-start');
                    const end_id = this.getAttribute('data-end');
                    
                    const formData = new FormData();
                    formData.append('start_id', start_id);
                    formData.append('end_id', end_id);
                    
                    fetch('/api/delete_range', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('编号段删除成功');
                            window.location.reload();
                        } else {
                            alert('删除失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('删除错误:', error);
                        alert('删除过程中发生错误');
                    });
                }
            });
        });
        
        // 导入编号段
        document.getElementById('import-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('/api/import_ranges', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('导入成功: ' + data.message);
                    window.location.reload();
                } else {
                    alert('导入失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('导入错误:', error);
                alert('导入过程中发生错误');
            });
        });
        
        // 导出编号段
        document.getElementById('export-ranges').addEventListener('click', function() {
            fetch('/api/export_ranges')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = data.file_url;
                    } else {
                        alert('导出失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('导出错误:', error);
                    alert('导出过程中发生错误');
                });
        });
    </script>
</body>
</html>