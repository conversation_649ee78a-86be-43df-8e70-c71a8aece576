from flask import Flask, render_template, request, jsonify, redirect, url_for, send_from_directory
import os
import pandas as pd
import numpy as np
from datetime import datetime
import json

app = Flask(__name__)

# HTTPS重定向中间件
@app.before_request
def force_https():
    """强制使用HTTPS协议，提高安全性和摄像头兼容性"""
    # 检查用户代理，微信浏览器可能对自签名证书有问题
    user_agent = request.headers.get('User-Agent', '')
    is_wechat = 'MicroMessenger' in user_agent

    # 微信浏览器暂时不强制HTTPS，避免证书问题
    if is_wechat:
        return None

    # 检查是否有SSL证书文件
    ssl_cert = 'ssl/server.crt'
    ssl_key = 'ssl/server.key'

    # 只有在有SSL证书且不是本地开发环境时才强制HTTPS
    if (os.path.exists(ssl_cert) and os.path.exists(ssl_key) and
        not request.is_secure and
        request.headers.get('X-Forwarded-Proto', 'http') != 'https' and
        request.host not in ['localhost:5050', '127.0.0.1:5050']):

        # 构建HTTPS URL
        https_url = request.url.replace('http://', 'https://', 1)
        return redirect(https_url, code=301)

# 配置静态文件缓存和压缩
@app.after_request
def add_cache_headers(response):
    """添加缓存头和压缩以提高性能"""
    # 静态资源缓存1天
    if request.endpoint == 'static':
        response.cache_control.max_age = 86400  # 1天
        response.cache_control.public = True
        # 添加压缩提示
        if request.path.endswith(('.css', '.js', '.html')):
            response.headers['Vary'] = 'Accept-Encoding'
    # HTML页面缓存5分钟
    elif request.endpoint in ['index', 'scan', 'admin', 'admin_ranges', 'admin_products']:
        response.cache_control.max_age = 300  # 5分钟
        response.cache_control.public = True
        response.headers['Vary'] = 'Accept-Encoding'
    # API响应不缓存
    elif request.endpoint and request.endpoint.startswith('api_'):
        response.cache_control.no_cache = True
        response.cache_control.no_store = True

    # 添加安全头
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'SAMEORIGIN'  # 改为SAMEORIGIN以支持微信内嵌
    response.headers['X-XSS-Protection'] = '1; mode=block'

    # 微信浏览器特殊处理
    user_agent = request.headers.get('User-Agent', '')
    is_wechat = 'MicroMessenger' in user_agent

    if is_wechat:
        # 微信浏览器不缓存，确保内容能正确显示
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        # 支持跨域访问
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
        # 微信浏览器兼容性头
        response.headers['X-Frame-Options'] = 'ALLOWALL'

    return response

# 配置
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['DATABASE_FOLDER'] = 'database'
app.config['RANGE_FILE'] = 'product_ranges.xlsx'
app.config['PRODUCT_FILE'] = 'products.xlsx'

# 确保必要的目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['DATABASE_FOLDER'], exist_ok=True)

# 初始化数据库文件
def init_database():
    # 创建编号段表
    range_file = os.path.join(app.config['DATABASE_FOLDER'], app.config['RANGE_FILE'])
    if not os.path.exists(range_file):
        df_ranges = pd.DataFrame({
            'start_id': [],
            'end_id': [],
            'product_code': [],
            'spec_info': [],
            'special_req': [],
            'customer': []
        })
        df_ranges.to_excel(range_file, index=False)
    
    # 创建产品信息表
    product_file = os.path.join(app.config['DATABASE_FOLDER'], app.config['PRODUCT_FILE'])
    if not os.path.exists(product_file):
        df_products = pd.DataFrame({
            'uid': [],
            'product_code': [],
            'spec_info': [],
            'ratio_param': [],
            'special_req': [],
            'customer': [],
            'coil_weight': [],
            'final_weight': [],
            'update_time': []
        })
        df_products.to_excel(product_file, index=False)

# 查找产品编号对应的产品代码和规格信息
def find_product_info(product_id):
    range_file = os.path.join(app.config['DATABASE_FOLDER'], app.config['RANGE_FILE'])
    
    try:
        df_ranges = pd.read_excel(range_file)
    except Exception as e:
        print(f"读取产品编号段数据库失败: {e}")
        return None
    
    # 记录调试信息
    print(f"查询产品ID: {product_id}, 类型: {type(product_id)}")
    
    if df_ranges.empty:
        print("警告: 产品编号段数据库为空")
        return None
        
    # 确保product_id是整数类型进行比较
    try:
        product_id_int = int(product_id)
        print(f"产品ID已转换为整数: {product_id_int}")
    except ValueError:
        # 如果无法转换为整数，则保持原样进行字符串比较
        product_id_int = product_id
        print(f"无法将产品ID转换为整数: {product_id}，使用字符串比较")
    
    for _, row in df_ranges.iterrows():
        # 将start_id和end_id转换为整数进行比较
        try:
            start_id_int = int(row['start_id'])
            end_id_int = int(row['end_id'])
            
            # 检查编号是否在范围内（整数比较）
            if start_id_int <= product_id_int <= end_id_int:
                # 确保所有字段都是有效的JSON值（处理NaN值和numpy类型）
                product_code = str(row['product_code']) if pd.notna(row['product_code']) else ''
                spec_info = str(row['spec_info']) if pd.notna(row['spec_info']) else ''

                ratio_param = str(row.get('ratio_param', '')) if pd.notna(row.get('ratio_param')) else ''
                special_req = str(row.get('special_req', '')) if pd.notna(row.get('special_req')) else ''
                customer = str(row.get('customer', '')) if pd.notna(row.get('customer')) else ''
                
                return {
                    'product_code': product_code,
                    'spec_info': spec_info,
                    'ratio_param': ratio_param,
                    'special_req': special_req,
                    'customer': customer
                }
        except ValueError:
            # 如果无法转换为整数，则使用字符串比较
            start_id = str(row['start_id'])
            end_id = str(row['end_id'])
            
            # 检查编号是否在范围内（字符串比较，仅作为备用方案）
            if start_id <= str(product_id) <= end_id:
                # 确保所有字段都是有效的JSON值（处理NaN值和numpy类型）
                product_code = str(row['product_code']) if pd.notna(row['product_code']) else ''
                spec_info = str(row['spec_info']) if pd.notna(row['spec_info']) else ''
                ratio_param = str(row.get('ratio_param', '')) if pd.notna(row.get('ratio_param')) else ''
                special_req = str(row.get('special_req', '')) if pd.notna(row.get('special_req')) else ''
                customer = str(row.get('customer', '')) if pd.notna(row.get('customer')) else ''
                
                return {
                    'product_code': product_code,
                    'spec_info': spec_info,
                    'ratio_param': ratio_param,
                    'special_req': special_req,
                    'customer': customer
                }
    
    return None

# 获取或创建产品记录
def get_or_create_product(product_id):
    product_file = os.path.join(app.config['DATABASE_FOLDER'], app.config['PRODUCT_FILE'])
    df_products = pd.read_excel(product_file)
    
    # 确保product_id是字符串类型
    product_id_str = str(product_id)
    
    # 查找现有记录 - 确保类型一致性
    existing_product = df_products[df_products['uid'].astype(str) == product_id_str]
    
    # 记录调试信息
    print(f"查找产品ID: {product_id_str}, 找到记录数: {len(existing_product)}")
    
    if not existing_product.empty:
        product = existing_product.iloc[0].to_dict()
        # 确保所有字段都是Python原生类型，避免numpy类型导致的JSON序列化错误
        for key, value in product.items():
            if pd.isna(value):
                product[key] = '' if isinstance(value, str) else None
            elif isinstance(value, (np.int64, np.int32, np.int16, np.int8)):
                product[key] = int(value)
            elif isinstance(value, (np.float64, np.float32, np.float16)):
                product[key] = float(value)
            elif isinstance(value, np.bool_):
                product[key] = bool(value)
        return product
    
    # 查找产品信息
    product_info = find_product_info(product_id)
    if not product_info:
        return None
    
    # 查找是否有相同产品代码的记录，获取其重量信息
    product_code = product_info['product_code']
    same_code_products = df_products[df_products['product_code'] == product_code]
    
    # 默认重量值
    coil_weight = 0.0
    final_weight = 0.0
    
    # 如果存在相同产品代码的记录，使用其最新的重量信息
    if not same_code_products.empty:
        # 按更新时间排序，获取最新的记录
        latest_product = same_code_products.sort_values('update_time', ascending=False).iloc[0]
        coil_weight = latest_product['coil_weight']
        final_weight = latest_product['final_weight']
    
    # 创建新记录
    new_product = {
        'uid': product_id,
        'product_code': product_info['product_code'],
        'spec_info': product_info['spec_info'],
        'ratio_param': product_info.get('ratio_param', ''),
        'special_req': product_info.get('special_req', ''),
        'customer': product_info.get('customer', ''),
        'coil_weight': coil_weight,
        'final_weight': final_weight,
        'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    
    # 确保所有字段都是有效的JSON值（处理NaN值和numpy类型）
    for key, value in new_product.items():
        if pd.isna(value):
            new_product[key] = '' if isinstance(value, str) else None
        elif isinstance(value, (np.int64, np.int32, np.int16, np.int8)):
            new_product[key] = int(value)
        elif isinstance(value, (np.float64, np.float32, np.float16)):
            new_product[key] = float(value)
        elif isinstance(value, np.bool_):
            new_product[key] = bool(value)
    
    # 添加到数据库
    df_products = pd.concat([df_products, pd.DataFrame([new_product])], ignore_index=True)
    df_products.to_excel(product_file, index=False)
    
    return new_product

# 更新产品重量
def update_product_weight(product_id, coil_weight=None, final_weight=None):
    product_file = os.path.join(app.config['DATABASE_FOLDER'], app.config['PRODUCT_FILE'])
    df_products = pd.read_excel(product_file)
    
    # 确保product_id是字符串类型
    product_id = str(product_id)
    
    # 查找产品
    product_mask = df_products['uid'].astype(str) == product_id
    if not any(product_mask):
        return False
    
    # 更新重量
    if coil_weight is not None:
        df_products.loc[product_mask, 'coil_weight'] = coil_weight
    
    if final_weight is not None:
        df_products.loc[product_mask, 'final_weight'] = final_weight
    
    # 更新时间
    df_products.loc[product_mask, 'update_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 保存更改
    df_products.to_excel(product_file, index=False)
    
    # 同步相同产品代码的重量
    product_code = df_products.loc[product_mask, 'product_code'].iloc[0]
    sync_weights_by_code(product_code, coil_weight, final_weight)
    
    return True

# 同步相同产品代码的重量
def sync_weights_by_code(product_code, coil_weight=None, final_weight=None):
    product_file = os.path.join(app.config['DATABASE_FOLDER'], app.config['PRODUCT_FILE'])
    df_products = pd.read_excel(product_file)
    
    # 查找相同产品代码的记录
    code_mask = df_products['product_code'] == product_code
    
    # 更新重量
    if coil_weight is not None:
        df_products.loc[code_mask, 'coil_weight'] = coil_weight
    
    if final_weight is not None:
        df_products.loc[code_mask, 'final_weight'] = final_weight
    
    # 更新时间
    df_products.loc[code_mask, 'update_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 保存更改
    df_products.to_excel(product_file, index=False)

# 路由
@app.route('/')
def index():
    # 检测用户代理
    user_agent = request.headers.get('User-Agent', '')
    is_wechat = 'MicroMessenger' in user_agent

    # 如果是微信浏览器，重定向到专门的引导页面
    if is_wechat:
        return render_template('wechat_guide.html')

    return render_template('index.html')

@app.route('/wechat-guide')
def wechat_guide():
    """微信浏览器引导页面"""
    return render_template('wechat_guide.html')

@app.route('/help')
def help_page():
    """简单的帮助页面，纯HTML无依赖"""
    return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>使用帮助 - 称重管理系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        h1 { color: #333; text-align: center; }
        .alert { padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; margin: 15px 0; }
        .step { margin: 15px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .url { background: #e9ecef; padding: 10px; border-radius: 5px; font-family: monospace; word-break: break-all; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 称重管理系统</h1>
        <h2>微信用户使用指南</h2>

        <div class="alert">
            <strong>重要：</strong>微信浏览器限制了摄像头功能，请在手机浏览器中打开使用。
        </div>

        <div class="step">
            <strong>步骤1：</strong>点击微信右上角"..."菜单
        </div>

        <div class="step">
            <strong>步骤2：</strong>选择"在浏览器中打开"
        </div>

        <div class="step">
            <strong>步骤3：</strong>在浏览器中允许摄像头权限
        </div>

        <h3>📋 手动复制链接：</h3>
        <div class="url" id="url"></div>

        <div style="text-align: center; margin-top: 20px;">
            <a href="/wechat-simple" class="btn">使用简化版</a>
            <a href="javascript:copyUrl()" class="btn">复制链接</a>
        </div>

        <script>
            document.getElementById('url').textContent = window.location.origin;
            function copyUrl() {
                const url = window.location.origin;
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(url).then(() => alert('链接已复制！'));
                } else {
                    alert('请手动复制链接：' + url);
                }
            }
        </script>
    </div>
</body>
</html>
    '''

@app.route('/scan')
def scan():
    return render_template('scan.html')

@app.route('/mobile-compatibility')
def mobile_compatibility():
    """移动端兼容性检测页面"""
    return render_template('mobile_compatibility.html')

@app.route('/wechat-simple')
def wechat_simple():
    """微信浏览器简化版页面"""
    return render_template('wechat_simple.html')

@app.route('/product/<product_id>')
def product_detail(product_id):
    product = get_or_create_product(product_id)
    if not product:
        return render_template('error.html', message='产品编号不存在或无法匹配到产品代码')
    
    return render_template('product.html', product=product)

@app.route('/api/find_product', methods=['POST'])
def api_find_product():
    product_id = request.form.get('product_id')
    if not product_id:
        print("API错误: 产品编号为空")
        return jsonify({'success': False, 'message': '产品编号不能为空'})
    
    print(f"API请求: 查询产品ID {product_id}")
    try:
        product = get_or_create_product(product_id)
        if not product:
            print(f"API错误: 产品ID {product_id} 不存在或无法匹配到产品代码")
            return jsonify({'success': False, 'message': '产品编号不存在或无法匹配到产品代码'})
        
        # 确保所有字段都是有效的JSON值（处理NaN值和numpy类型）
        for key, value in product.items():
            if pd.isna(value):
                product[key] = '' if isinstance(value, str) else None
            elif isinstance(value, (np.int64, np.int32, np.int16, np.int8)):
                product[key] = int(value)
            elif isinstance(value, (np.float64, np.float32, np.float16)):
                product[key] = float(value)
            elif isinstance(value, np.bool_):
                product[key] = bool(value)
        
        print(f"API成功: 找到产品 {product['uid']}")
        return jsonify({'success': True, 'product': product})
    except Exception as e:
        print(f"API异常: {str(e)}")
        return jsonify({'success': False, 'message': f'查询产品信息时发生错误: {str(e)}'})

@app.route('/api/update_weight', methods=['POST'])
def api_update_weight():
    product_id = request.form.get('product_id')
    coil_weight = request.form.get('coil_weight')
    final_weight = request.form.get('final_weight')
    
    if not product_id:
        return jsonify({'success': False, 'message': '产品编号不能为空'})
        
    # 确保product_id是字符串类型
    product_id = str(product_id)
    
    # 转换重量
    try:
        coil_weight = float(coil_weight) if coil_weight else None
        final_weight = float(final_weight) if final_weight else None
    except ValueError:
        return jsonify({'success': False, 'message': '重量必须是有效的数字'})
    
    # 更新重量
    success = update_product_weight(product_id, coil_weight, final_weight)
    if not success:
        return jsonify({'success': False, 'message': '产品不存在'})
    
    return jsonify({'success': True, 'message': '重量更新成功'})

# 管理界面路由
@app.route('/admin')
def admin():
    return render_template('admin.html')

@app.route('/admin/ranges')
def admin_ranges():
    range_file = os.path.join(app.config['DATABASE_FOLDER'], app.config['RANGE_FILE'])
    df_ranges = pd.read_excel(range_file)
    ranges = df_ranges.to_dict('records')
    
    # 确保所有字段都是Python原生类型，避免numpy类型导致的JSON序列化错误
    for item in ranges:
        for key, value in list(item.items()):
            if pd.isna(value):
                item[key] = '' if isinstance(value, str) else None
            elif isinstance(value, (np.int64, np.int32, np.int16, np.int8)):
                item[key] = int(value)
            elif isinstance(value, (np.float64, np.float32, np.float16)):
                item[key] = float(value)
            elif isinstance(value, np.bool_):
                item[key] = bool(value)
    
    return render_template('admin_ranges.html', ranges=ranges)

@app.route('/admin/products')
def admin_products():
    product_file = os.path.join(app.config['DATABASE_FOLDER'], app.config['PRODUCT_FILE'])
    df_products = pd.read_excel(product_file)
    products = df_products.to_dict('records')
    
    # 确保所有字段都是Python原生类型，避免numpy类型导致的JSON序列化错误
    for item in products:
        for key, value in list(item.items()):
            if pd.isna(value):
                item[key] = '' if isinstance(value, str) else None
            elif isinstance(value, (np.int64, np.int32, np.int16, np.int8)):
                item[key] = int(value)
            elif isinstance(value, (np.float64, np.float32, np.float16)):
                item[key] = float(value)
            elif isinstance(value, np.bool_):
                item[key] = bool(value)
    
    return render_template('admin_products.html', products=products)

@app.route('/api/add_range', methods=['POST'])
def api_add_range():
    start_id = request.form.get('start_id')
    end_id = request.form.get('end_id')
    product_code = request.form.get('product_code')
    spec_info = request.form.get('spec_info')
    special_req = request.form.get('special_req', '')
    customer = request.form.get('customer', '')
    
    if not all([start_id, end_id, product_code, spec_info]):
        return jsonify({'success': False, 'message': '必填字段不能为空'})
    
    # 添加新范围
    range_file = os.path.join(app.config['DATABASE_FOLDER'], app.config['RANGE_FILE'])
    df_ranges = pd.read_excel(range_file)
    
    new_range = {
        'start_id': start_id,
        'end_id': end_id,
        'product_code': product_code,
        'spec_info': spec_info,
        'special_req': special_req,
        'customer': customer
    }
    
    df_ranges = pd.concat([df_ranges, pd.DataFrame([new_range])], ignore_index=True)
    df_ranges.to_excel(range_file, index=False)
    
    return jsonify({'success': True, 'message': '编号段添加成功'})

@app.route('/api/delete_range', methods=['POST'])
def api_delete_range():
    start_id = request.form.get('start_id')
    end_id = request.form.get('end_id')
    
    if not all([start_id, end_id]):
        return jsonify({'success': False, 'message': '起始和结束编号不能为空'})
    
    # 删除范围
    range_file = os.path.join(app.config['DATABASE_FOLDER'], app.config['RANGE_FILE'])
    df_ranges = pd.read_excel(range_file)
    
    # 确保数据类型一致进行比较
    mask = (df_ranges['start_id'].astype(str) == str(start_id)) & (df_ranges['end_id'].astype(str) == str(end_id))
    if not any(mask):
        return jsonify({'success': False, 'message': '找不到指定的编号段'})
    
    df_ranges = df_ranges[~mask]
    df_ranges.to_excel(range_file, index=False)
    
    return jsonify({'success': True, 'message': '编号段删除成功'})

@app.route('/api/delete_product', methods=['POST'])
def api_delete_product():
    product_id = request.form.get('uid')
    if not product_id:
        return jsonify({'success': False, 'message': '产品ID不能为空'})
    
    # 删除产品
    product_file = os.path.join(app.config['DATABASE_FOLDER'], app.config['PRODUCT_FILE'])
    df_products = pd.read_excel(product_file)
    
    # 确保数据类型一致进行比较
    mask = df_products['uid'].astype(str) == str(product_id)
    if not any(mask):
        return jsonify({'success': False, 'message': '找不到指定的产品'})
    
    df_products = df_products[~mask]
    df_products.to_excel(product_file, index=False)
    
    return jsonify({'success': True, 'message': '产品删除成功'})

@app.route('/api/import_ranges', methods=['POST'])
def api_import_ranges():
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': '没有上传文件'})
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': '没有选择文件'})
    
    if not file.filename.endswith('.xlsx'):
        return jsonify({'success': False, 'message': '只支持Excel文件(.xlsx)'})
    
    # 保存上传的文件
    upload_path = os.path.join(app.config['UPLOAD_FOLDER'], 'ranges_import.xlsx')
    file.save(upload_path)
    
    try:
        # 读取上传的文件
        df_import = pd.read_excel(upload_path)
        
        # 验证必要的列
        required_columns = ['start_id', 'end_id', 'product_code', 'spec_info']
        for col in required_columns:
            if col not in df_import.columns:
                return jsonify({'success': False, 'message': f'导入文件缺少必要的列: {col}'})
        
        # 读取现有数据
        range_file = os.path.join(app.config['DATABASE_FOLDER'], app.config['RANGE_FILE'])
        df_ranges = pd.read_excel(range_file)
        
        # 合并数据
        df_ranges = pd.concat([df_ranges, df_import], ignore_index=True)
        
        # 保存合并后的数据
        df_ranges.to_excel(range_file, index=False)
        
        return jsonify({'success': True, 'message': f'成功导入 {len(df_import)} 条编号段记录'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'导入失败: {str(e)}'})

@app.route('/api/export_ranges', methods=['GET'])
def api_export_ranges():
    range_file = os.path.join(app.config['DATABASE_FOLDER'], app.config['RANGE_FILE'])
    export_path = os.path.join(app.config['UPLOAD_FOLDER'], 'ranges_export.xlsx')
    
    # 复制文件
    df_ranges = pd.read_excel(range_file)
    df_ranges.to_excel(export_path, index=False)
    
    return jsonify({'success': True, 'file_url': url_for('download_file', filename='ranges_export.xlsx')})

@app.route('/api/export_products', methods=['GET'])
def api_export_products():
    product_file = os.path.join(app.config['DATABASE_FOLDER'], app.config['PRODUCT_FILE'])
    export_path = os.path.join(app.config['UPLOAD_FOLDER'], 'products_export.xlsx')
    
    # 读取并去重数据
    df_products = pd.read_excel(product_file)
    # 按产品编号和产品代码分组，保留每组中更新时间最新的记录
    df_products = df_products.sort_values('update_time', ascending=False).drop_duplicates(['uid', 'product_code'])
    df_products.to_excel(export_path, index=False)
    
    return jsonify({'success': True, 'file_url': url_for('download_file', filename='products_export.xlsx')})

@app.route('/download/<filename>')
def download_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename, as_attachment=True)

@app.route('/static/wechat-help.html')
def wechat_help_static():
    """提供静态的微信帮助页面"""
    return send_from_directory('static', 'wechat-help.html')

# 初始化数据库
init_database()

if __name__ == '__main__':
    # 检查SSL证书文件是否存在
    ssl_cert = 'ssl/server.crt'
    ssl_key = 'ssl/server.key'

    if os.path.exists(ssl_cert) and os.path.exists(ssl_key):
        print("使用HTTPS模式启动服务器...")
        print("访问地址: https://localhost:5050 或 https://您的IP:5050")
        print("注意: 首次访问时浏览器会显示安全警告，请选择'继续访问'或'高级设置'->'继续前往'")
        app.run(host='0.0.0.0', port=5050, debug=True, ssl_context=(ssl_cert, ssl_key))
    else:
        print("SSL证书不存在，使用HTTP模式启动...")
        print("警告: HTTP模式下无法使用摄像头功能")
        print("请运行 python generate_ssl_cert.py 生成SSL证书")
        app.run(host='0.0.0.0', port=5050, debug=True)