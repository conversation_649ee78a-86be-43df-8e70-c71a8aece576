<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端兼容性检测 - 称重管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap-icons.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        .compatibility-item {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .compatibility-pass {
            background-color: #d1edff;
            border-color: #0d6efd;
        }
        
        .compatibility-fail {
            background-color: #f8d7da;
            border-color: #dc3545;
        }
        
        .compatibility-warning {
            background-color: #fff3cd;
            border-color: #ffc107;
        }
        
        .test-result {
            font-weight: bold;
            margin-left: 10px;
        }
        
        .browser-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">称重管理系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/scan">扫码称重</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/mobile-compatibility">兼容性检测</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="bi bi-phone me-2"></i>移动端兼容性检测</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            <strong>检测说明：</strong>此页面将检测您的设备和浏览器是否支持扫码称重功能，并提供针对性的优化建议。
                        </div>

                        <!-- 浏览器环境信息 -->
                        <div class="browser-info">
                            <h5><i class="bi bi-browser-chrome me-2"></i>浏览器环境信息</h5>
                            <div id="browser-info-content">
                                <p>正在检测浏览器环境...</p>
                            </div>
                        </div>

                        <!-- 兼容性检测结果 -->
                        <div class="compatibility-tests">
                            <h5><i class="bi bi-check-circle me-2"></i>兼容性检测结果</h5>
                            
                            <div id="https-test" class="compatibility-item">
                                <i class="bi bi-shield-lock me-2"></i>
                                HTTPS协议支持
                                <span class="test-result" id="https-result">检测中...</span>
                            </div>
                            
                            <div id="camera-api-test" class="compatibility-item">
                                <i class="bi bi-camera me-2"></i>
                                摄像头API支持
                                <span class="test-result" id="camera-api-result">检测中...</span>
                            </div>
                            
                            <div id="media-devices-test" class="compatibility-item">
                                <i class="bi bi-camera-video me-2"></i>
                                媒体设备枚举支持
                                <span class="test-result" id="media-devices-result">检测中...</span>
                            </div>
                            
                            <div id="webrtc-test" class="compatibility-item">
                                <i class="bi bi-broadcast me-2"></i>
                                WebRTC支持
                                <span class="test-result" id="webrtc-result">检测中...</span>
                            </div>
                            
                            <div id="canvas-test" class="compatibility-item">
                                <i class="bi bi-image me-2"></i>
                                Canvas支持
                                <span class="test-result" id="canvas-result">检测中...</span>
                            </div>
                        </div>

                        <!-- 优化建议 -->
                        <div class="mt-4">
                            <h5><i class="bi bi-lightbulb me-2"></i>优化建议</h5>
                            <div id="recommendations" class="alert alert-secondary">
                                正在生成优化建议...
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <button class="btn btn-secondary me-md-2" onclick="location.reload()">
                                <i class="bi bi-arrow-clockwise me-1"></i> 重新检测
                            </button>
                            <a href="/scan" class="btn btn-primary">
                                <i class="bi bi-qr-code-scan me-1"></i> 开始扫码
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script>
        // 浏览器环境检测函数（复用scan.html中的函数）
        function isMobileDevice() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }

        function isWeChatBrowser() {
            return /MicroMessenger/i.test(navigator.userAgent);
        }

        function isiOSSafari() {
            const ua = navigator.userAgent;
            return /iPad|iPhone|iPod/.test(ua) && /Safari/.test(ua) && !/CriOS|FxiOS|OPiOS|mercury/i.test(ua);
        }

        function isiOSDevice() {
            return /iPad|iPhone|iPod/.test(navigator.userAgent);
        }

        function getBrowserEnvironment() {
            const ua = navigator.userAgent;
            const env = {
                isWeChat: isWeChatBrowser(),
                isiOS: isiOSDevice(),
                isiOSSafari: isiOSSafari(),
                isMobile: isMobileDevice(),
                isAndroid: /Android/i.test(ua),
                userAgent: ua
            };
            
            if (env.isWeChat) {
                env.browserType = 'wechat';
                env.browserName = '微信浏览器';
            } else if (env.isiOSSafari) {
                env.browserType = 'ios-safari';
                env.browserName = 'iOS Safari';
            } else if (env.isiOS) {
                env.browserType = 'ios-other';
                env.browserName = 'iOS其他浏览器';
            } else if (env.isAndroid) {
                env.browserType = 'android';
                env.browserName = 'Android浏览器';
            } else {
                env.browserType = 'desktop';
                env.browserName = '桌面浏览器';
            }
            
            return env;
        }

        // 显示浏览器信息
        function displayBrowserInfo() {
            const env = getBrowserEnvironment();
            const infoContent = document.getElementById('browser-info-content');
            
            infoContent.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>浏览器类型：</strong>${env.browserName}</p>
                        <p><strong>设备类型：</strong>${env.isMobile ? '移动设备' : '桌面设备'}</p>
                        <p><strong>操作系统：</strong>${env.isiOS ? 'iOS' : (env.isAndroid ? 'Android' : '其他')}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>屏幕分辨率：</strong>${screen.width} × ${screen.height}</p>
                        <p><strong>视窗大小：</strong>${window.innerWidth} × ${window.innerHeight}</p>
                        <p><strong>设备像素比：</strong>${window.devicePixelRatio || 1}</p>
                    </div>
                </div>
                <details class="mt-2">
                    <summary>详细User Agent信息</summary>
                    <p class="mt-2 text-muted small">${env.userAgent}</p>
                </details>
            `;
        }

        // 兼容性检测
        function runCompatibilityTests() {
            const tests = [
                testHTTPS,
                testCameraAPI,
                testMediaDevices,
                testWebRTC,
                testCanvas
            ];
            
            tests.forEach(test => test());
            
            // 延迟生成建议，等待所有测试完成
            setTimeout(generateRecommendations, 1000);
        }

        function testHTTPS() {
            const element = document.getElementById('https-test');
            const result = document.getElementById('https-result');
            
            if (location.protocol === 'https:') {
                element.classList.add('compatibility-pass');
                result.innerHTML = '<i class="bi bi-check-circle-fill text-success"></i> 支持';
            } else {
                element.classList.add('compatibility-warning');
                result.innerHTML = '<i class="bi bi-exclamation-triangle-fill text-warning"></i> 使用HTTP协议';
            }
        }

        function testCameraAPI() {
            const element = document.getElementById('camera-api-test');
            const result = document.getElementById('camera-api-result');
            
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                element.classList.add('compatibility-pass');
                result.innerHTML = '<i class="bi bi-check-circle-fill text-success"></i> 支持';
            } else {
                element.classList.add('compatibility-fail');
                result.innerHTML = '<i class="bi bi-x-circle-fill text-danger"></i> 不支持';
            }
        }

        function testMediaDevices() {
            const element = document.getElementById('media-devices-test');
            const result = document.getElementById('media-devices-result');
            
            if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
                element.classList.add('compatibility-pass');
                result.innerHTML = '<i class="bi bi-check-circle-fill text-success"></i> 支持';
            } else {
                element.classList.add('compatibility-fail');
                result.innerHTML = '<i class="bi bi-x-circle-fill text-danger"></i> 不支持';
            }
        }

        function testWebRTC() {
            const element = document.getElementById('webrtc-test');
            const result = document.getElementById('webrtc-result');
            
            if (window.RTCPeerConnection || window.webkitRTCPeerConnection || window.mozRTCPeerConnection) {
                element.classList.add('compatibility-pass');
                result.innerHTML = '<i class="bi bi-check-circle-fill text-success"></i> 支持';
            } else {
                element.classList.add('compatibility-fail');
                result.innerHTML = '<i class="bi bi-x-circle-fill text-danger"></i> 不支持';
            }
        }

        function testCanvas() {
            const element = document.getElementById('canvas-test');
            const result = document.getElementById('canvas-result');
            
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                if (ctx) {
                    element.classList.add('compatibility-pass');
                    result.innerHTML = '<i class="bi bi-check-circle-fill text-success"></i> 支持';
                } else {
                    element.classList.add('compatibility-fail');
                    result.innerHTML = '<i class="bi bi-x-circle-fill text-danger"></i> 不支持';
                }
            } catch (e) {
                element.classList.add('compatibility-fail');
                result.innerHTML = '<i class="bi bi-x-circle-fill text-danger"></i> 不支持';
            }
        }

        function generateRecommendations() {
            const env = getBrowserEnvironment();
            const recommendationsEl = document.getElementById('recommendations');
            let recommendations = [];
            
            // 检查各项测试结果
            const httpsPass = document.getElementById('https-test').classList.contains('compatibility-pass');
            const cameraPass = document.getElementById('camera-api-test').classList.contains('compatibility-pass');
            const mediaPass = document.getElementById('media-devices-test').classList.contains('compatibility-pass');
            
            if (!httpsPass) {
                recommendations.push('建议使用HTTPS协议访问以确保摄像头功能正常工作');
            }
            
            if (!cameraPass || !mediaPass) {
                if (env.isWeChat) {
                    recommendations.push('微信浏览器可能限制摄像头访问，建议点击右上角"..."选择"在浏览器中打开"');
                } else if (env.isiOS) {
                    recommendations.push('建议使用Safari浏览器，并确保iOS版本为11.0或更高');
                } else {
                    recommendations.push('建议使用Chrome、Firefox、Edge等现代浏览器');
                }
            }
            
            if (env.isMobile) {
                recommendations.push('移动设备建议在WiFi环境下使用以获得更好的性能');
                recommendations.push('确保设备有足够的存储空间和内存');
            }
            
            if (recommendations.length === 0) {
                recommendationsEl.className = 'alert alert-success';
                recommendationsEl.innerHTML = '<i class="bi bi-check-circle-fill me-2"></i>您的设备和浏览器完全兼容扫码称重功能！';
            } else {
                recommendationsEl.className = 'alert alert-warning';
                const listItems = recommendations.map(r => `<li>${r}</li>`).join('');
                recommendationsEl.innerHTML = `<i class="bi bi-exclamation-triangle-fill me-2"></i><strong>建议：</strong><ul class="mb-0 mt-2">${listItems}</ul>`;
            }
        }

        // 页面加载完成后执行检测
        document.addEventListener('DOMContentLoaded', function() {
            displayBrowserInfo();
            runCompatibilityTests();
        });
    </script>
</body>
</html>
