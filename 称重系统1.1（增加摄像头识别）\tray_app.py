import os
import sys
import threading
import webbrowser
import subprocess
import time
import signal
from PIL import Image, ImageDraw
import pystray
from pystray import MenuItem as item

# 全局变量
flask_process = None
app_running = False
app_port = 5050
app_host = '0.0.0.0'

# 创建图标
def create_image(width, height, color1, color2):
    # 创建一个新的图像，背景为透明
    image = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    dc = ImageDraw.Draw(image)
    
    # 绘制一个简单的称重图标
    # 绘制秤盘
    dc.rectangle(
        [(width * 0.2, height * 0.6), (width * 0.8, height * 0.7)],
        fill=color1
    )
    # 绘制支柱
    dc.rectangle(
        [(width * 0.45, height * 0.7), (width * 0.55, height * 0.9)],
        fill=color1
    )
    # 绘制底座
    dc.rectangle(
        [(width * 0.3, height * 0.9), (width * 0.7, height * 0.95)],
        fill=color1
    )
    # 绘制显示屏
    dc.rectangle(
        [(width * 0.3, height * 0.3), (width * 0.7, height * 0.5)],
        fill=color2
    )
    # 绘制连接线
    dc.line(
        [(width * 0.5, height * 0.5), (width * 0.5, height * 0.6)],
        fill=color1,
        width=2
    )
    
    return image

# 启动Flask应用
def start_flask_app():
    global flask_process, app_running
    
    if app_running:
        return
    
    try:
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        app_path = os.path.join(current_dir, 'app.py')
        
        # 使用subprocess启动Flask应用
        flask_process = subprocess.Popen(
            [sys.executable, app_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NO_WINDOW  # 在Windows上隐藏控制台窗口
        )
        
        app_running = True
        print(f"Flask应用已启动，进程ID: {flask_process.pid}")
        
        # 等待服务器启动
        time.sleep(2)
    except Exception as e:
        print(f"启动Flask应用时出错: {e}")

# 停止Flask应用
def stop_flask_app():
    global flask_process, app_running
    
    if not app_running or flask_process is None:
        return
    
    try:
        # 在Windows上使用taskkill强制终止进程及其子进程
        subprocess.run(['taskkill', '/F', '/T', '/PID', str(flask_process.pid)], 
                      stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        flask_process = None
        app_running = False
        print("Flask应用已停止")
    except Exception as e:
        print(f"停止Flask应用时出错: {e}")

# 在浏览器中打开应用
def open_in_browser():
    if not app_running:
        start_flask_app()
        # 给服务器一些启动时间
        time.sleep(2)

    # 检查是否有SSL证书，优先使用HTTPS
    ssl_cert = 'ssl/server.crt'
    ssl_key = 'ssl/server.key'

    if os.path.exists(ssl_cert) and os.path.exists(ssl_key):
        url = f"https://localhost:{app_port}"
        print(f"使用HTTPS访问: {url}")
    else:
        url = f"http://localhost:{app_port}"
        print(f"使用HTTP访问: {url}")
        print("警告: HTTP模式下无法使用摄像头功能")

    webbrowser.open(url)

# 更新图标状态
def update_icon_status(icon):
    if app_running:
        icon.icon = create_image(64, 64, (0, 128, 0), (0, 255, 0))  # 绿色表示运行中
    else:
        icon.icon = create_image(64, 64, (128, 128, 128), (200, 200, 200))  # 灰色表示停止

# 启动/停止服务的处理函数
def on_toggle_service(icon, item):
    global app_running
    if app_running:
        stop_flask_app()
    else:
        start_flask_app()
    update_icon_status(icon)

# 打开浏览器的处理函数
def on_open_browser(icon, item):
    open_in_browser()

# 退出应用的处理函数
def on_exit(icon, item):
    stop_flask_app()
    icon.stop()

# 主函数
def main():
    # 创建菜单
    menu = (
        item('启动/停止服务', on_toggle_service, checked=lambda item: app_running),
        item('打开浏览器访问', on_open_browser),
        item('退出', on_exit)
    )
    
    # 创建图标
    icon = pystray.Icon(
        "称重管理系统",
        create_image(64, 64, (128, 128, 128), (200, 200, 200)),  # 初始为灰色
        "称重管理系统",
        menu=menu
    )
    
    # 自动启动Flask应用
    start_flask_app()
    
    # 更新图标状态
    update_icon_status(icon)
    
    # 运行系统托盘图标
    icon.run()

# 程序入口
if __name__ == "__main__":
    main()