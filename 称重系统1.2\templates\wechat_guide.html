<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>微信用户使用指南 - 称重管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .container {
            flex: 1;
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
            width: 100%;
        }
        
        .guide-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .guide-card h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            display: flex;
            align-items: center;
        }
        
        .guide-card h3::before {
            content: "📱";
            margin-right: 10px;
            font-size: 24px;
        }
        
        .step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }
        
        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }
        
        .step-desc {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .btn {
            display: block;
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
        }
        
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        
        .alert {
            padding: 15px;
            border-radius: 12px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }
        
        .alert-warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .alert-info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        .url-box {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            word-break: break-all;
            color: #495057;
        }
        
        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            font-size: 12px;
            margin-top: 10px;
            cursor: pointer;
        }
        
        .browser-icons {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }
        
        .browser-icon {
            text-align: center;
            padding: 10px;
        }
        
        .browser-icon div {
            font-size: 32px;
            margin-bottom: 5px;
        }
        
        .browser-icon span {
            font-size: 12px;
            color: #666;
        }
        
        .footer {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 称重管理系统</h1>
        <p>微信用户专用访问指南</p>
    </div>

    <div class="container">
        <div class="alert alert-warning">
            <strong>⚠️ 重要提示：</strong><br>
            由于微信浏览器的安全限制，扫码称重功能无法在微信中正常使用。<br>
            请按照以下步骤在手机浏览器中打开以获得完整功能。
        </div>

        <div class="guide-card">
            <h3>如何在浏览器中打开</h3>
            
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <div class="step-title">点击右上角菜单</div>
                    <div class="step-desc">在微信聊天界面，点击右上角的"..."按钮</div>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <div class="step-title">选择在浏览器中打开</div>
                    <div class="step-desc">在弹出菜单中选择"在浏览器中打开"或"用Safari打开"</div>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <div class="step-title">允许摄像头权限</div>
                    <div class="step-desc">在浏览器中首次使用时，请允许网站访问摄像头权限</div>
                </div>
            </div>
        </div>

        <div class="guide-card">
            <h3>📋 手动复制链接</h3>
            <p>如果上述方法不可用，请复制以下链接在浏览器中打开：</p>
            
            <div class="url-box" id="url-box">
                正在获取当前网址...
            </div>
            
            <button class="copy-btn" onclick="copyUrl()">📋 复制链接</button>
        </div>

        <div class="guide-card">
            <h3>🌐 推荐浏览器</h3>
            <div class="browser-icons">
                <div class="browser-icon">
                    <div>🧭</div>
                    <span>Safari</span>
                </div>
                <div class="browser-icon">
                    <div>🔵</div>
                    <span>Chrome</span>
                </div>
                <div class="browser-icon">
                    <div>🦊</div>
                    <span>Firefox</span>
                </div>
                <div class="browser-icon">
                    <div>🔷</div>
                    <span>Edge</span>
                </div>
            </div>
        </div>

        <div class="alert alert-info">
            <strong>💡 小贴士：</strong><br>
            • iOS用户推荐使用Safari浏览器<br>
            • Android用户推荐使用Chrome浏览器<br>
            • 确保浏览器版本较新以获得最佳体验
        </div>

        <!-- 应急功能按钮 -->
        <a href="/wechat-simple" class="btn btn-outline">🔧 使用简化版功能</a>
        <button class="btn" onclick="forceOpen()">🚀 强制尝试打开完整版</button>
    </div>

    <div class="footer">
        <p>如有问题，请联系系统管理员</p>
        <p>或尝试在电脑浏览器中访问</p>
    </div>

    <script>
        // 获取当前URL并显示
        function updateUrl() {
            const currentUrl = window.location.origin;
            const urlBox = document.getElementById('url-box');
            if (urlBox) {
                urlBox.textContent = currentUrl;
            }
        }

        // 复制URL到剪贴板
        function copyUrl() {
            const url = window.location.origin;
            
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(url).then(function() {
                    alert('✅ 链接已复制到剪贴板！\n请在浏览器中粘贴访问。');
                }).catch(function() {
                    fallbackCopy(url);
                });
            } else {
                fallbackCopy(url);
            }
        }

        // 备用复制方法
        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                alert('✅ 链接已复制！\n请在浏览器中粘贴访问：\n' + text);
            } catch (err) {
                alert('📋 请手动复制以下链接：\n' + text);
            }
            
            document.body.removeChild(textArea);
        }

        // 强制尝试打开完整版
        function forceOpen() {
            if (confirm('⚠️ 注意：微信浏览器可能无法正常显示扫码功能。\n\n是否仍要尝试打开？\n\n建议点击"取消"后按照上述步骤在浏览器中打开。')) {
                window.location.href = '/scan';
            }
        }

        // 尝试打开外部浏览器（仅在某些环境下有效）
        function openInExternalBrowser() {
            const url = window.location.origin;
            
            // 尝试不同的方法打开外部浏览器
            const methods = [
                () => window.open(url, '_blank'),
                () => window.location.href = 'googlechrome://navigate?url=' + encodeURIComponent(url),
                () => window.location.href = 'firefox://open-url?url=' + encodeURIComponent(url),
                () => window.location.href = url
            ];
            
            let success = false;
            for (let method of methods) {
                try {
                    method();
                    success = true;
                    break;
                } catch (e) {
                    console.log('尝试打开外部浏览器失败:', e);
                }
            }
            
            if (!success) {
                copyUrl();
            }
        }

        // 检测是否真的在微信中
        function isInWeChat() {
            return /MicroMessenger/i.test(navigator.userAgent);
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            updateUrl();
            
            // 如果不在微信中，提示用户
            if (!isInWeChat()) {
                setTimeout(function() {
                    if (confirm('检测到您可能不在微信浏览器中。\n是否直接跳转到系统首页？')) {
                        window.location.href = '/';
                    }
                }, 1000);
            }
            
            // 添加一些调试信息
            console.log('User Agent:', navigator.userAgent);
            console.log('当前URL:', window.location.href);
        });
    </script>
</body>
</html>
