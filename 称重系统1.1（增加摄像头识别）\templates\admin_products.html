<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品信息管理 - 称重管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap-icons.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">称重管理系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/scan">扫码称重</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin">管理后台</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>产品信息管理</h2>
                    <a href="/admin" class="btn btn-secondary">返回管理首页</a>
                </div>
                
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">产品列表</h5>
                        <button id="export-products" class="btn btn-sm btn-light">导出数据</button>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-search"></i></span>
                                <input type="text" id="product-search" class="form-control" placeholder="搜索产品编号、产品代码或型号规格...">
                                <button id="clear-search" class="btn btn-outline-secondary">清除</button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>产品编号</th>
                                        <th>产品代码</th>
                                        <th>型号规格</th>
                                        <th>线圈重量(kg)</th>
                                        <th>成品重量(kg)</th>
                                        <th>更新时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% set seen = {} %}
                                    {% for product in products|sort(attribute='update_time', reverse=true) %}
                                        {% if not seen.get(product.uid ~ product.product_code) %}
                                            {% set _ = seen.update({product.uid ~ product.product_code: true}) %}
                                            <tr>
                                                <td>{{ product.uid }}</td>
                                                <td>{{ product.product_code }}</td>
                                                <td>{{ product.spec_info }}</td>
                                                <td>{{ product.coil_weight }}</td>
                                                <td>{{ product.final_weight }}</td>
                                                <td>{{ product.update_time }}</td>
                                                <td>
                                                    <button class="btn btn-danger btn-sm delete-product" data-uid="{{ product.uid }}">删除</button>
                                                </td>
                                            </tr>
                                        {% endif %}
                                    {% endfor %}
                                    {% if not products %}
                                    <tr>
                                        <td colspan="6" class="text-center">暂无产品数据</td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script>
        // 搜索功能实现
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('product-search');
            const clearButton = document.getElementById('clear-search');
            const tableRows = document.querySelectorAll('tbody tr');
            
            // 搜索过滤功能
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();
                
                tableRows.forEach(row => {
                    if (!row.querySelector('td[colspan]')) { // 跳过无数据提示行
                        const uid = row.cells[0].textContent.toLowerCase();
                        const productCode = row.cells[1].textContent.toLowerCase();
                        const specInfo = row.cells[2].textContent.toLowerCase();
                        
                        if (uid.includes(searchTerm) || 
                            productCode.includes(searchTerm) || 
                            specInfo.includes(searchTerm)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    }
                });
                
                // 检查是否所有行都被隐藏
                checkNoResults();
            });
            
            // 清除搜索
            clearButton.addEventListener('click', function() {
                searchInput.value = '';
                tableRows.forEach(row => {
                    row.style.display = '';
                });
                
                // 移除可能存在的"无搜索结果"行
                const existingNoResultRow = document.querySelector('.no-results-row');
                if (existingNoResultRow) {
                    existingNoResultRow.remove();
                }
            });
            
            // 检查是否需要显示"无搜索结果"行
            function checkNoResults() {
                let visibleRows = 0;
                tableRows.forEach(row => {
                    if (row.style.display !== 'none' && !row.querySelector('td[colspan]')) {
                        visibleRows++;
                    }
                });
                
                // 移除可能存在的"无搜索结果"行
                const existingNoResultRow = document.querySelector('.no-results-row');
                if (existingNoResultRow) {
                    existingNoResultRow.remove();
                }
                
                if (visibleRows === 0 && searchInput.value.trim() !== '') {
                    const tbody = document.querySelector('tbody');
                    const noResultRow = document.createElement('tr');
                    noResultRow.className = 'no-results-row';
                    noResultRow.innerHTML = '<td colspan="7" class="text-center">没有找到匹配的产品</td>';
                    tbody.appendChild(noResultRow);
                }
            }
        });
        
        // 导出产品数据
        document.getElementById('export-products').addEventListener('click', function() {
            fetch('/api/export_products')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = data.file_url;
                    } else {
                        alert('导出失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('导出错误:', error);
                    alert('导出过程中发生错误');
                });
        });

        // 删除产品
        document.querySelectorAll('.delete-product').forEach(button => {
            button.addEventListener('click', function() {
                if (confirm('确定要删除这个产品吗？')) {
                    const uid = this.getAttribute('data-uid');
                    const formData = new FormData();
                    formData.append('uid', uid);

                    fetch('/api/delete_product', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('产品删除成功');
                            window.location.reload();
                        } else {
                            alert('删除失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('删除错误:', error);
                        alert('删除过程中发生错误');
                    });
                }
            });
        });
    </script>
</body>
</html>