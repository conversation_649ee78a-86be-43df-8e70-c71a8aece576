<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>微信版 - 称重管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .header p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .container {
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .btn {
            display: block;
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .browser-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .manual-input {
            margin-top: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
        }
        
        .input-group .form-control {
            flex: 1;
        }
        
        .input-group .btn {
            flex: 0 0 auto;
            width: auto;
            padding: 12px 20px;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>称重管理系统</h1>
        <p>微信浏览器专用版</p>
    </div>

    <div class="container">
        <!-- 浏览器检测信息 -->
        <div class="browser-info" id="browser-info">
            正在检测浏览器环境...
        </div>

        <!-- 微信提示 -->
        <div class="alert alert-info">
            <strong>微信用户提示：</strong><br>
            由于微信浏览器的限制，建议点击右上角"..."选择"在浏览器中打开"以获得完整功能。
        </div>

        <!-- 功能卡片 -->
        <div class="card">
            <h3>🔍 手动查询产品</h3>
            <p>输入产品编号查询产品信息和重量数据</p>
            
            <div class="manual-input">
                <form id="search-form">
                    <div class="form-group">
                        <label for="product-id">产品编号</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="product-id" placeholder="请输入产品编号" required>
                            <button type="submit" class="btn">查询</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <h3>📱 功能说明</h3>
            <ul class="feature-list">
                <li>手动输入产品编号查询信息</li>
                <li>查看产品规格和重量数据</li>
                <li>支持重量信息录入和更新</li>
                <li>数据自动同步保存</li>
            </ul>
        </div>

        <div class="card">
            <h3>🚀 获得完整功能</h3>
            <p>要使用扫码功能，请选择以下方式之一：</p>
            <a href="/scan" class="btn">尝试扫码功能</a>
            <button class="btn btn-secondary" onclick="openInBrowser()">在浏览器中打开</button>
            <a href="/mobile-compatibility" class="btn btn-secondary">兼容性检测</a>
        </div>

        <!-- 使用说明 -->
        <div class="card">
            <h3>📖 使用说明</h3>
            <div class="alert alert-warning">
                <strong>微信中使用限制：</strong><br>
                1. 摄像头功能可能受限<br>
                2. 某些高级功能不可用<br>
                3. 建议在浏览器中打开获得最佳体验
            </div>
            
            <p><strong>推荐操作：</strong></p>
            <ol>
                <li>点击微信右上角"..."菜单</li>
                <li>选择"在浏览器中打开"</li>
                <li>允许浏览器摄像头权限</li>
                <li>享受完整的扫码称重功能</li>
            </ol>
        </div>
    </div>

    <script>
        // 简化的浏览器检测
        function detectBrowser() {
            const ua = navigator.userAgent;
            const isWeChat = /MicroMessenger/i.test(ua);
            const isiOS = /iPad|iPhone|iPod/.test(ua);
            const isAndroid = /Android/i.test(ua);
            
            let browserName = '未知浏览器';
            let tips = '';
            
            if (isWeChat) {
                browserName = '微信浏览器';
                tips = '检测到微信环境，部分功能可能受限';
            } else if (isiOS) {
                browserName = 'iOS设备';
                tips = '建议使用Safari浏览器';
            } else if (isAndroid) {
                browserName = 'Android设备';
                tips = '建议使用Chrome浏览器';
            }
            
            document.getElementById('browser-info').innerHTML = `
                <strong>浏览器：</strong>${browserName}<br>
                <strong>提示：</strong>${tips}<br>
                <strong>屏幕：</strong>${window.innerWidth} × ${window.innerHeight}
            `;
        }
        
        // 手动查询表单
        document.getElementById('search-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const productId = document.getElementById('product-id').value.trim();
            if (productId) {
                // 跳转到产品详情页
                window.location.href = '/product/' + encodeURIComponent(productId);
            } else {
                alert('请输入产品编号');
            }
        });
        
        // 在浏览器中打开
        function openInBrowser() {
            // 复制当前URL到剪贴板（如果支持）
            const url = window.location.origin + '/scan';
            
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(url).then(function() {
                    alert('链接已复制到剪贴板！\n请在浏览器中粘贴访问：\n' + url);
                }).catch(function() {
                    alert('请手动复制以下链接在浏览器中打开：\n' + url);
                });
            } else {
                alert('请手动复制以下链接在浏览器中打开：\n' + url);
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            detectBrowser();
            
            // 如果不是微信浏览器，自动跳转到完整版
            if (!/MicroMessenger/i.test(navigator.userAgent)) {
                setTimeout(function() {
                    if (confirm('检测到您使用的不是微信浏览器，是否跳转到完整版？')) {
                        window.location.href = '/scan';
                    }
                }, 2000);
            }
        });
    </script>
</body>
</html>
