/**
 * 微信浏览器检测和重定向脚本
 * 用于检测微信环境并引导用户到浏览器中打开
 */

(function() {
    'use strict';
    
    // 检测是否在微信浏览器中
    function isWeChatBrowser() {
        return /MicroMessenger/i.test(navigator.userAgent);
    }
    
    // 检测是否在移动设备上
    function isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }
    
    // 检测iOS设备
    function isiOSDevice() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
    }
    
    // 创建遮罩层引导用户
    function createWeChatGuide() {
        // 检查是否已经创建过引导层
        if (document.getElementById('wechat-guide-overlay')) {
            return;
        }
        
        const overlay = document.createElement('div');
        overlay.id = 'wechat-guide-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 999999;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            text-align: center;
            padding: 20px;
        `;
        
        const isiOS = isiOSDevice();
        const guideText = isiOS ? 
            '请点击右上角"..."<br>选择"在Safari中打开"' : 
            '请点击右上角"..."<br>选择"在浏览器中打开"';
        
        overlay.innerHTML = `
            <div style="max-width: 300px;">
                <div style="font-size: 60px; margin-bottom: 20px;">📱</div>
                <h2 style="margin-bottom: 20px; font-size: 24px;">微信浏览器限制</h2>
                <p style="font-size: 18px; line-height: 1.5; margin-bottom: 30px;">
                    ${guideText}<br>
                    以获得完整功能
                </p>
                <div style="margin-bottom: 20px;">
                    <button onclick="closeGuide()" style="
                        background: #007bff;
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 6px;
                        font-size: 16px;
                        margin: 5px;
                        cursor: pointer;
                    ">我知道了</button>
                    <button onclick="copyCurrentUrl()" style="
                        background: #28a745;
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 6px;
                        font-size: 16px;
                        margin: 5px;
                        cursor: pointer;
                    ">复制链接</button>
                </div>
                <p style="font-size: 14px; opacity: 0.8;">
                    或访问简化版：<br>
                    <a href="/wechat-simple" style="color: #17a2b8; text-decoration: underline;">
                        ${window.location.origin}/wechat-simple
                    </a>
                </p>
            </div>
        `;
        
        document.body.appendChild(overlay);
        
        // 添加全局函数
        window.closeGuide = function() {
            overlay.style.display = 'none';
        };
        
        window.copyCurrentUrl = function() {
            const url = window.location.origin;
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(url).then(function() {
                    alert('✅ 链接已复制到剪贴板！\n请在浏览器中粘贴访问。');
                }).catch(function() {
                    fallbackCopy(url);
                });
            } else {
                fallbackCopy(url);
            }
        };
        
        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                alert('✅ 链接已复制！\n' + text);
            } catch (err) {
                alert('📋 请手动复制链接：\n' + text);
            }
            document.body.removeChild(textArea);
        }
    }
    
    // 显示简单的顶部提示条
    function createSimpleNotice() {
        const notice = document.createElement('div');
        notice.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: #ff6b6b;
            color: white;
            text-align: center;
            padding: 10px;
            z-index: 999998;
            font-size: 14px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        `;
        
        notice.innerHTML = `
            <span>⚠️ 微信浏览器限制，请在浏览器中打开</span>
            <button onclick="this.parentNode.style.display='none'" style="
                background: none;
                border: none;
                color: white;
                margin-left: 10px;
                cursor: pointer;
                font-size: 16px;
            ">×</button>
        `;
        
        document.body.appendChild(notice);
        
        // 3秒后自动隐藏
        setTimeout(() => {
            if (notice.parentNode) {
                notice.style.display = 'none';
            }
        }, 5000);
    }
    
    // 尝试重定向到引导页面
    function redirectToGuide() {
        // 如果当前不在引导页面，则重定向
        if (!window.location.pathname.includes('wechat-guide') && 
            !window.location.pathname.includes('help') &&
            !window.location.pathname.includes('wechat-simple')) {
            
            // 延迟重定向，给页面一些加载时间
            setTimeout(() => {
                window.location.href = '/wechat-guide';
            }, 1000);
        }
    }
    
    // 主函数
    function handleWeChatBrowser() {
        if (!isWeChatBrowser()) {
            return; // 不是微信浏览器，无需处理
        }
        
        console.log('检测到微信浏览器环境');
        
        // 根据页面类型选择不同的处理方式
        const path = window.location.pathname;
        
        if (path === '/' || path === '/scan') {
            // 主页或扫码页面，显示完整引导
            createWeChatGuide();
        } else if (path.includes('admin')) {
            // 管理页面，显示简单提示
            createSimpleNotice();
        } else {
            // 其他页面，显示简单提示
            createSimpleNotice();
        }
        
        // 记录访问日志
        try {
            console.log('微信浏览器访问:', {
                userAgent: navigator.userAgent,
                url: window.location.href,
                timestamp: new Date().toISOString()
            });
        } catch (e) {
            // 忽略日志错误
        }
    }
    
    // 页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', handleWeChatBrowser);
    } else {
        handleWeChatBrowser();
    }
    
    // 导出函数供外部使用
    window.WeChatRedirect = {
        isWeChatBrowser: isWeChatBrowser,
        isMobileDevice: isMobileDevice,
        isiOSDevice: isiOSDevice,
        createGuide: createWeChatGuide,
        createNotice: createSimpleNotice
    };
    
})();
