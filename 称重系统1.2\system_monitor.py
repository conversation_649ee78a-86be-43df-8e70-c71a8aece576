#!/usr/bin/env python3
"""
系统监控脚本
检查称重系统的性能状态和配置
"""

import os
import sys
import time
import requests
import json
from datetime import datetime

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    exists = os.path.exists(file_path)
    status = "✓" if exists else "✗"
    size = ""
    if exists:
        try:
            size_bytes = os.path.getsize(file_path)
            if size_bytes > 1024 * 1024:
                size = f" ({size_bytes / (1024*1024):.1f}MB)"
            elif size_bytes > 1024:
                size = f" ({size_bytes / 1024:.1f}KB)"
            else:
                size = f" ({size_bytes}B)"
        except:
            size = ""
    
    print(f"  {status} {description}{size}")
    return exists

def check_static_resources():
    """检查静态资源状态"""
    print("📁 静态资源检查:")
    
    resources = [
        ("static/css/bootstrap.min.css", "Bootstrap CSS"),
        ("static/css/bootstrap-icons.css", "Bootstrap Icons CSS"),
        ("static/js/bootstrap.bundle.min.js", "Bootstrap JavaScript"),
        ("static/js/html5-qrcode.min.js", "QR Code Scanner"),
        ("static/fonts/bootstrap-icons.woff2", "Icons字体文件"),
        ("static/css/style.css", "自定义样式")
    ]
    
    all_exist = True
    for file_path, description in resources:
        if not check_file_exists(file_path, description):
            all_exist = False
    
    if all_exist:
        print("  🎉 所有静态资源完整")
    else:
        print("  ⚠️  部分静态资源缺失，建议运行: python download_assets.py")
    
    return all_exist

def check_ssl_certificates():
    """检查SSL证书状态"""
    print("\n🔒 SSL证书检查:")
    
    cert_exists = check_file_exists("ssl/server.crt", "SSL证书文件")
    key_exists = check_file_exists("ssl/server.key", "SSL私钥文件")
    
    if cert_exists and key_exists:
        print("  🎉 SSL证书配置完整")
        
        # 检查证书有效期
        try:
            from cryptography import x509
            from cryptography.hazmat.primitives import serialization
            
            with open("ssl/server.crt", "rb") as f:
                cert_data = f.read()
            
            cert = x509.load_pem_x509_certificate(cert_data)
            expiry_date = cert.not_valid_after
            days_left = (expiry_date - datetime.now()).days
            
            if days_left > 30:
                print(f"  ✓ 证书有效期: {days_left}天")
            elif days_left > 0:
                print(f"  ⚠️  证书即将过期: {days_left}天")
            else:
                print(f"  ✗ 证书已过期: {abs(days_left)}天前")
                
        except Exception as e:
            print(f"  ⚠️  无法检查证书有效期: {e}")
    else:
        print("  ⚠️  SSL证书缺失，建议运行: python generate_ssl_cert.py")
    
    return cert_exists and key_exists

def check_database_files():
    """检查数据库文件状态"""
    print("\n💾 数据库文件检查:")
    
    db_files = [
        ("database/product_ranges.xlsx", "产品编号段数据"),
        ("database/products.xlsx", "产品信息数据")
    ]
    
    all_exist = True
    for file_path, description in db_files:
        if not check_file_exists(file_path, description):
            all_exist = False
    
    if all_exist:
        print("  🎉 数据库文件完整")
    else:
        print("  ℹ️  数据库文件将在首次运行时自动创建")
    
    return all_exist

def test_server_connection(url, timeout=5):
    """测试服务器连接"""
    try:
        response = requests.get(url, timeout=timeout, verify=False)
        return response.status_code, response.elapsed.total_seconds()
    except requests.exceptions.Timeout:
        return None, "超时"
    except requests.exceptions.ConnectionError:
        return None, "连接失败"
    except Exception as e:
        return None, str(e)

def check_server_status():
    """检查服务器状态"""
    print("\n🌐 服务器状态检查:")
    
    urls = [
        ("https://localhost:5050", "HTTPS本地访问"),
        ("http://localhost:5050", "HTTP本地访问")
    ]
    
    server_running = False
    for url, description in urls:
        print(f"  测试 {description}...")
        status_code, response_time = test_server_connection(url)
        
        if status_code:
            if isinstance(response_time, float):
                print(f"    ✓ 状态码: {status_code}, 响应时间: {response_time:.3f}秒")
                server_running = True
            else:
                print(f"    ✗ {response_time}")
        else:
            print(f"    ✗ {response_time}")
    
    if not server_running:
        print("  ℹ️  服务器未运行，使用以下命令启动:")
        print("     python start_optimized.py")
    
    return server_running

def check_python_environment():
    """检查Python环境"""
    print("🐍 Python环境检查:")
    
    print(f"  ✓ Python版本: {sys.version.split()[0]}")
    
    required_packages = [
        "flask", "pandas", "openpyxl", "cryptography", 
        "requests", "pystray", "PIL"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✓ {package}")
        except ImportError:
            print(f"  ✗ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"  ⚠️  缺失包: {', '.join(missing_packages)}")
        print("     运行: pip install -r requirements.txt")
        return False
    else:
        print("  🎉 所有依赖包完整")
        return True

def generate_performance_report():
    """生成性能报告"""
    print("\n📊 性能优化状态:")
    
    score = 0
    max_score = 5
    
    # 检查静态资源本地化
    if os.path.exists("static/css/bootstrap.min.css"):
        print("  ✓ 静态资源本地化 (+20分)")
        score += 1
    else:
        print("  ✗ 静态资源本地化 (0分)")
    
    # 检查HTTPS配置
    if os.path.exists("ssl/server.crt"):
        print("  ✓ HTTPS安全访问 (+20分)")
        score += 1
    else:
        print("  ✗ HTTPS安全访问 (0分)")
    
    # 检查移动端优化
    if os.path.exists("start_optimized.py"):
        print("  ✓ 移动端优化配置 (+20分)")
        score += 1
    else:
        print("  ✗ 移动端优化配置 (0分)")
    
    # 检查缓存策略
    try:
        with open("app.py", "r", encoding="utf-8") as f:
            content = f.read()
            if "cache_control" in content:
                print("  ✓ 缓存策略优化 (+20分)")
                score += 1
            else:
                print("  ✗ 缓存策略优化 (0分)")
    except:
        print("  ✗ 缓存策略优化 (0分)")
    
    # 检查响应式设计
    try:
        with open("static/css/style.css", "r", encoding="utf-8") as f:
            content = f.read()
            if "@media" in content:
                print("  ✓ 响应式设计优化 (+20分)")
                score += 1
            else:
                print("  ✗ 响应式设计优化 (0分)")
    except:
        print("  ✗ 响应式设计优化 (0分)")
    
    percentage = (score / max_score) * 100
    print(f"\n  📈 性能优化得分: {score}/{max_score} ({percentage:.0f}%)")
    
    if percentage >= 80:
        print("  🎉 优秀！系统已充分优化")
    elif percentage >= 60:
        print("  👍 良好，还有提升空间")
    else:
        print("  ⚠️  需要优化，建议运行完整优化流程")

def main():
    """主函数"""
    print("=" * 50)
    print("🔍 称重管理系统 - 性能监控报告")
    print("=" * 50)
    print(f"📅 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 环境检查
    env_ok = check_python_environment()
    
    # 文件检查
    static_ok = check_static_resources()
    ssl_ok = check_ssl_certificates()
    db_ok = check_database_files()
    
    # 服务器检查
    server_ok = check_server_status()
    
    # 性能报告
    generate_performance_report()
    
    print("\n" + "=" * 50)
    print("📋 建议操作:")
    
    if not static_ok:
        print("  1. 运行 python download_assets.py 下载静态资源")
    
    if not ssl_ok:
        print("  2. 运行 python generate_ssl_cert.py 生成SSL证书")
    
    if not env_ok:
        print("  3. 运行 pip install -r requirements.txt 安装依赖")
    
    if not server_ok:
        print("  4. 运行 python start_optimized.py 启动优化服务器")
    
    if static_ok and ssl_ok and env_ok:
        print("  🎉 系统状态良好！可以正常使用")
        if not server_ok:
            print("  💡 运行 python start_optimized.py 启动服务器")
    
    print("=" * 50)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n检查被用户中断")
    except Exception as e:
        print(f"\n检查过程中发生错误: {e}")
    
    input("\n按回车键退出...")
