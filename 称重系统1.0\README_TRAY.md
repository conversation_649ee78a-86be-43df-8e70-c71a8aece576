# 称重管理系统 - 系统托盘应用

## 概述

这是称重管理系统的系统托盘应用版本，它允许您通过系统托盘图标来控制称重管理系统的运行。系统托盘应用提供以下功能：

- 在系统托盘中显示应用程序状态（运行/停止）
- 通过托盘菜单启动/停止称重管理系统服务
- 快速在浏览器中打开称重管理系统
- 一键退出应用程序

## 安装步骤

### 1. 安装依赖包

```
pip install -r requirements.txt
```

这将安装以下依赖：
- flask：Web应用框架
- pandas：数据处理
- openpyxl：Excel文件操作
- pystray：系统托盘功能
- Pillow：图像处理（用于创建托盘图标）
- pyinstaller：用于打包应用为可执行文件

### 2. 运行系统托盘应用

```
python tray_app.py
```

运行后，您将在系统托盘区域看到称重管理系统的图标。图标颜色表示应用状态：
- 绿色：服务正在运行
- 灰色：服务已停止

### 3. 使用系统托盘菜单

右键点击系统托盘图标，将显示以下菜单选项：
- **启动/停止服务**：切换称重管理系统服务的运行状态
- **打开浏览器访问**：在默认浏览器中打开称重管理系统界面
- **退出**：完全退出应用程序（包括系统托盘图标和后台服务）

## 打包为可执行文件

如果您希望将应用打包为独立的可执行文件（.exe），可以使用以下命令：

```
python build.py
```

打包完成后，您可以在 `dist` 目录中找到 `称重管理系统.exe` 文件。双击该文件即可运行系统托盘应用，无需安装Python环境。

## 注意事项

1. 系统托盘应用默认在 `http://localhost:5050` 上运行称重管理系统服务
2. 首次启动时，应用会自动启动称重管理系统服务
3. 如果您的计算机上已经有其他应用占用了5050端口，请修改 `tray_app.py` 文件中的 `app_port` 变量
4. 打包的可执行文件包含了所有必要的依赖和资源，可以在没有Python环境的计算机上运行