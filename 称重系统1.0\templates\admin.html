<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - 称重管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">称重管理系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/scan">扫码称重</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin">管理后台</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8 text-center mb-4">
                <h1 class="mb-3">称重管理系统后台</h1>
                <p class="lead">管理产品编号段和查看产品信息</p>
            </div>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">编号段管理</h5>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <p>管理产品编号段、产品代码和规格信息的对应关系</p>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>添加新的编号段</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>删除现有编号段</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>批量导入/导出编号段</li>
                                </ul>
                                <a href="/admin/ranges" class="btn btn-primary mt-auto">进入管理</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">产品信息管理</h5>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <p>查看所有产品的信息和重量数据</p>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>查看产品编号列表</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>查看产品重量信息</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>导出产品数据</li>
                                </ul>
                                <a href="/admin/products" class="btn btn-primary mt-auto">进入管理</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
</body>
</html>