import os
import sys
import shutil
from pathlib import Path

# 获取当前脚本所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 确保在正确的目录中
os.chdir(current_dir)

# 创建build和dist目录（如果不存在）
os.makedirs('build', exist_ok=True)
os.makedirs('dist', exist_ok=True)

# 清理之前的构建文件
print("清理之前的构建文件...")
for path in ['build', 'dist']:
    for item in os.listdir(path):
        item_path = os.path.join(path, item)
        if os.path.isdir(item_path):
            shutil.rmtree(item_path)
        else:
            os.remove(item_path)

# 使用PyInstaller打包应用
print("开始打包应用...")

# 构建命令
cmd = f"""\
pyinstaller \
--name="称重管理系统" \
--icon="{os.path.join(current_dir, 'static', 'css', 'favicon.ico')}" \
--add-data="{os.path.join(current_dir, 'templates')}:templates" \
--add-data="{os.path.join(current_dir, 'static')}:static" \
--add-data="{os.path.join(current_dir, 'database')}:database" \
--add-data="{os.path.join(current_dir, 'uploads')}:uploads" \
--noconsole \
--onefile \
{os.path.join(current_dir, 'tray_app.py')}\
"""

# 在Windows上使用分号作为路径分隔符
cmd = cmd.replace(':', ';')

# 执行打包命令
print("执行打包命令...")
print(cmd)
os.system(cmd)

print("\n打包完成！")
print(f"可执行文件位于: {os.path.join(current_dir, 'dist', '称重管理系统.exe')}")