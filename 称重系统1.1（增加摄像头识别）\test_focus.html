<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>摄像头对焦功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .video-container {
            max-width: 500px;
            margin: 0 auto;
            border: 2px solid #0d6efd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        #test-video {
            width: 100%;
            height: auto;
        }
        
        .focus-controls {
            max-width: 500px;
            margin: 20px auto;
        }
        
        .focus-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <h2 class="text-center mb-4">
                    <i class="bi bi-camera-reels me-2"></i>摄像头对焦功能测试
                </h2>
                
                <!-- 视频显示区域 -->
                <div class="video-container mb-3">
                    <video id="test-video" autoplay muted playsinline></video>
                </div>
                
                <!-- 控制按钮 -->
                <div class="text-center mb-3">
                    <button id="start-camera" class="btn btn-primary me-2">
                        <i class="bi bi-camera-video me-1"></i>启动摄像头
                    </button>
                    <button id="stop-camera" class="btn btn-secondary" disabled>
                        <i class="bi bi-stop-circle me-1"></i>停止摄像头
                    </button>
                </div>
                
                <!-- 对焦控制 -->
                <div id="focus-controls" class="focus-controls" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-camera-reels me-2"></i>对焦控制</h6>
                        </div>
                        <div class="card-body">
                            <div class="row align-items-center mb-3">
                                <div class="col-md-6">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="auto-focus-toggle" checked>
                                        <label class="form-check-label" for="auto-focus-toggle">
                                            自动对焦
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <button id="focus-near" class="btn btn-outline-primary btn-sm me-1">
                                        <i class="bi bi-zoom-in"></i> 近焦
                                    </button>
                                    <button id="focus-far" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-zoom-out"></i> 远焦
                                    </button>
                                </div>
                            </div>
                            <div id="manual-focus-controls" style="display: none;">
                                <label for="focus-range" class="form-label">手动对焦距离</label>
                                <input type="range" class="form-range" id="focus-range" min="0" max="100" value="0" step="1">
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">近焦</small>
                                    <small class="text-muted">远焦</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 信息显示区域 -->
                <div id="focus-info" class="focus-info">
                    <h6><i class="bi bi-info-circle me-2"></i>摄像头信息</h6>
                    <div id="camera-capabilities">点击"启动摄像头"查看摄像头对焦能力</div>
                </div>
                
                <!-- 日志区域 -->
                <div class="mt-4">
                    <h6><i class="bi bi-terminal me-2"></i>调试日志</h6>
                    <div id="log-output" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 0.9rem;">
                        等待操作...
                    </div>
                    <button id="clear-log" class="btn btn-outline-secondary btn-sm mt-2">清空日志</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentStream = null;
        let videoTrack = null;
        let focusCapabilities = null;
        let autoFocusEnabled = true;

        const video = document.getElementById('test-video');
        const startBtn = document.getElementById('start-camera');
        const stopBtn = document.getElementById('stop-camera');
        const focusControls = document.getElementById('focus-controls');
        const autoFocusToggle = document.getElementById('auto-focus-toggle');
        const manualFocusControls = document.getElementById('manual-focus-controls');
        const focusRange = document.getElementById('focus-range');
        const focusNearBtn = document.getElementById('focus-near');
        const focusFarBtn = document.getElementById('focus-far');
        const logOutput = document.getElementById('log-output');
        const cameraCapabilities = document.getElementById('camera-capabilities');

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            console.log(logEntry);
            
            const logDiv = document.createElement('div');
            logDiv.textContent = logEntry;
            if (type === 'error') logDiv.style.color = '#ff6b6b';
            if (type === 'success') logDiv.style.color = '#51cf66';
            if (type === 'warning') logDiv.style.color = '#ffd43b';
            
            logOutput.appendChild(logDiv);
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        // 清空日志
        document.getElementById('clear-log').addEventListener('click', () => {
            logOutput.innerHTML = '';
        });

        // 启动摄像头
        startBtn.addEventListener('click', async () => {
            try {
                log('正在启动摄像头...');
                startBtn.disabled = true;
                
                const constraints = {
                    video: {
                        // 优化近距离对焦的配置
                        facingMode: "environment",
                        width: { ideal: 1280 },
                        height: { ideal: 720 },
                        focusMode: "continuous",
                        focusDistance: { ideal: 0.1 }, // 设置为近焦
                        advanced: [
                            { focusMode: "continuous" },
                            { focusDistance: { min: 0, max: 1, ideal: 0.1 } }
                        ]
                    }
                };

                currentStream = await navigator.mediaDevices.getUserMedia(constraints);
                video.srcObject = currentStream;
                
                log('摄像头启动成功', 'success');
                startBtn.disabled = false;
                stopBtn.disabled = false;
                
                // 检查对焦能力
                checkFocusCapabilities();
                
            } catch (error) {
                log(`摄像头启动失败: ${error.message}`, 'error');
                startBtn.disabled = false;
            }
        });

        // 停止摄像头
        stopBtn.addEventListener('click', () => {
            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
                currentStream = null;
                video.srcObject = null;
                
                log('摄像头已停止');
                startBtn.disabled = false;
                stopBtn.disabled = true;
                focusControls.style.display = 'none';
                cameraCapabilities.textContent = '点击"启动摄像头"查看摄像头对焦能力';
            }
        });

        // 检查对焦能力
        function checkFocusCapabilities() {
            if (currentStream) {
                const tracks = currentStream.getVideoTracks();
                if (tracks.length > 0) {
                    videoTrack = tracks[0];
                    focusCapabilities = videoTrack.getCapabilities();
                    
                    log('摄像头对焦能力检查完成');
                    console.log('Focus capabilities:', focusCapabilities);
                    
                    // 显示能力信息
                    let capabilityText = '<strong>摄像头对焦能力:</strong><br>';
                    
                    if (focusCapabilities.focusMode) {
                        capabilityText += `• 对焦模式: ${focusCapabilities.focusMode.join(', ')}<br>`;
                    }
                    
                    if (focusCapabilities.focusDistance) {
                        capabilityText += `• 对焦距离范围: ${focusCapabilities.focusDistance.min} - ${focusCapabilities.focusDistance.max}<br>`;
                    }
                    
                    const supportsFocus = focusCapabilities.focusMode || focusCapabilities.focusDistance;
                    
                    if (supportsFocus) {
                        capabilityText += '<span class="text-success">✓ 支持对焦控制</span>';
                        focusControls.style.display = 'block';
                        log('摄像头支持对焦控制', 'success');
                        
                        // 初始化对焦控制
                        initializeFocusControls();
                    } else {
                        capabilityText += '<span class="text-warning">⚠ 不支持对焦控制</span>';
                        log('摄像头不支持对焦控制', 'warning');
                    }
                    
                    cameraCapabilities.innerHTML = capabilityText;
                }
            }
        }

        // 初始化对焦控制
        function initializeFocusControls() {
            // 自动对焦切换
            autoFocusToggle.addEventListener('change', function() {
                autoFocusEnabled = this.checked;
                if (autoFocusEnabled) {
                    manualFocusControls.style.display = 'none';
                    setAutoFocus(true);
                } else {
                    manualFocusControls.style.display = 'block';
                    setAutoFocus(false);
                }
            });

            // 手动对焦滑块
            focusRange.addEventListener('input', function() {
                if (!autoFocusEnabled && videoTrack) {
                    const focusDistance = parseFloat(this.value) / 100;
                    setManualFocus(focusDistance);
                }
            });

            // 近焦按钮
            focusNearBtn.addEventListener('click', function() {
                if (!autoFocusEnabled) {
                    const currentValue = parseInt(focusRange.value);
                    const newValue = Math.max(0, currentValue - 10);
                    focusRange.value = newValue;
                    setManualFocus(newValue / 100);
                }
            });

            // 远焦按钮
            focusFarBtn.addEventListener('click', function() {
                if (!autoFocusEnabled) {
                    const currentValue = parseInt(focusRange.value);
                    const newValue = Math.min(100, currentValue + 10);
                    focusRange.value = newValue;
                    setManualFocus(newValue / 100);
                }
            });
        }

        // 设置自动对焦
        function setAutoFocus(enabled) {
            if (videoTrack && focusCapabilities) {
                try {
                    if (enabled && focusCapabilities.focusMode && focusCapabilities.focusMode.includes('continuous')) {
                        videoTrack.applyConstraints({
                            advanced: [{ focusMode: 'continuous' }]
                        });
                        log('已启用自动对焦', 'success');
                    } else if (!enabled && focusCapabilities.focusMode && focusCapabilities.focusMode.includes('manual')) {
                        videoTrack.applyConstraints({
                            advanced: [{ focusMode: 'manual' }]
                        });
                        log('已切换到手动对焦', 'success');
                    }
                } catch (error) {
                    log(`设置对焦模式失败: ${error.message}`, 'error');
                }
            }
        }

        // 设置手动对焦距离
        function setManualFocus(distance) {
            if (videoTrack && focusCapabilities && focusCapabilities.focusDistance) {
                try {
                    videoTrack.applyConstraints({
                        advanced: [{ 
                            focusMode: 'manual',
                            focusDistance: distance 
                        }]
                    });
                    log(`设置对焦距离: ${distance.toFixed(2)}`, 'success');
                } catch (error) {
                    log(`设置对焦距离失败: ${error.message}`, 'error');
                }
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('对焦测试页面已加载');
        });
    </script>
</body>
</html>
