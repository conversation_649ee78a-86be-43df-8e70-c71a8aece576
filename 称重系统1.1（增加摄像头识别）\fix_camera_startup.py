#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摄像头启动问题快速修复脚本
解决Html5QrCode库加载和摄像头启动转圈问题
"""

import os
import sys
import requests
import shutil
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🔧 摄像头启动问题快速修复工具")
    print("   解决Html5QrCode库加载和摄像头启动转圈问题")
    print("=" * 60)
    print()

def check_file_exists(file_path):
    """检查文件是否存在"""
    return os.path.exists(file_path) and os.path.getsize(file_path) > 0

def download_library(url, save_path):
    """下载库文件"""
    try:
        print(f"📥 正在从 {url} 下载库文件...")
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        with open(save_path, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ 库文件下载成功，保存到: {save_path}")
        print(f"📊 文件大小: {os.path.getsize(save_path)} 字节")
        return True
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

def backup_existing_file(file_path):
    """备份现有文件"""
    if os.path.exists(file_path):
        backup_path = file_path + '.backup'
        shutil.copy2(file_path, backup_path)
        print(f"📋 已备份现有文件到: {backup_path}")

def fix_library_loading():
    """修复库加载问题"""
    print("🔍 检查Html5QrCode库文件...")
    
    # 定义文件路径
    static_js_dir = Path("static/js")
    library_file = static_js_dir / "html5-qrcode.min.js"
    
    # 确保目录存在
    static_js_dir.mkdir(parents=True, exist_ok=True)
    
    # 检查现有文件
    if check_file_exists(library_file):
        file_size = os.path.getsize(library_file)
        print(f"📁 找到现有库文件: {library_file}")
        print(f"📊 文件大小: {file_size} 字节")
        
        # 如果文件太小，可能是损坏的
        if file_size < 50000:  # 50KB
            print("⚠️ 库文件可能损坏（文件太小）")
            backup_existing_file(library_file)
        else:
            print("✅ 库文件看起来正常")
            return True
    else:
        print("❌ 未找到库文件")
    
    # 尝试下载库文件
    download_urls = [
        "https://unpkg.com/html5-qrcode@2.3.8/minified/html5-qrcode.min.js",
        "https://cdn.jsdelivr.net/npm/html5-qrcode@2.3.8/minified/html5-qrcode.min.js",
        "https://cdnjs.cloudflare.com/ajax/libs/html5-qrcode/2.3.8/html5-qrcode.min.js"
    ]
    
    for url in download_urls:
        if download_library(url, library_file):
            # 验证下载的文件
            if check_file_exists(library_file) and os.path.getsize(library_file) > 50000:
                print("✅ 库文件下载并验证成功")
                return True
            else:
                print("❌ 下载的文件验证失败")
                if os.path.exists(library_file):
                    os.remove(library_file)
    
    print("❌ 所有下载尝试都失败了")
    return False

def check_ssl_certificates():
    """检查SSL证书"""
    print("\n🔍 检查SSL证书...")
    
    cert_files = ["server.crt", "server.key"]
    all_exist = True
    
    for cert_file in cert_files:
        if os.path.exists(cert_file):
            print(f"✅ 找到证书文件: {cert_file}")
        else:
            print(f"❌ 缺少证书文件: {cert_file}")
            all_exist = False
    
    if not all_exist:
        print("💡 建议运行: python generate_ssl_cert.py")
    
    return all_exist

def check_dependencies():
    """检查Python依赖"""
    print("\n🔍 检查Python依赖...")
    
    required_packages = ["flask", "pandas", "openpyxl"]
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"💡 建议运行: pip install {' '.join(missing_packages)}")
    
    return len(missing_packages) == 0

def generate_test_report():
    """生成测试报告"""
    print("\n📋 生成测试报告...")
    
    report = []
    report.append("# 摄像头启动问题修复报告")
    report.append(f"生成时间: {__import__('datetime').datetime.now()}")
    report.append("")
    
    # 检查库文件
    library_file = Path("static/js/html5-qrcode.min.js")
    if check_file_exists(library_file):
        file_size = os.path.getsize(library_file)
        report.append(f"✅ Html5QrCode库文件: 存在 ({file_size} 字节)")
    else:
        report.append("❌ Html5QrCode库文件: 不存在")
    
    # 检查SSL证书
    cert_files = ["server.crt", "server.key"]
    for cert_file in cert_files:
        if os.path.exists(cert_file):
            report.append(f"✅ SSL证书 {cert_file}: 存在")
        else:
            report.append(f"❌ SSL证书 {cert_file}: 不存在")
    
    # 保存报告
    with open("camera_fix_report.txt", "w", encoding="utf-8") as f:
        f.write("\n".join(report))
    
    print("📄 测试报告已保存到: camera_fix_report.txt")

def main():
    """主函数"""
    print_banner()
    
    # 检查当前目录
    if not os.path.exists("app.py"):
        print("❌ 错误: 请在项目根目录运行此脚本")
        sys.exit(1)
    
    success_count = 0
    total_checks = 3
    
    # 修复库加载问题
    if fix_library_loading():
        success_count += 1
    
    # 检查SSL证书
    if check_ssl_certificates():
        success_count += 1
    
    # 检查依赖
    if check_dependencies():
        success_count += 1
    
    # 生成报告
    generate_test_report()
    
    # 总结
    print("\n" + "=" * 60)
    print("🏁 修复完成")
    print(f"✅ 成功: {success_count}/{total_checks}")
    
    if success_count == total_checks:
        print("🎉 所有检查都通过了！")
        print("💡 建议:")
        print("   1. 重启服务器: python app.py")
        print("   2. 访问: https://localhost:5050/library-test")
        print("   3. 测试摄像头功能")
    else:
        print("⚠️ 仍有问题需要解决，请查看上面的错误信息")
        print("💡 建议:")
        print("   1. 查看生成的报告: camera_fix_report.txt")
        print("   2. 访问调试页面: https://localhost:5050/camera-debug")
        print("   3. 如果问题持续，请联系技术支持")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
