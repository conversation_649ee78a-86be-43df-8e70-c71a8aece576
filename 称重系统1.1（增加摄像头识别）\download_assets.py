#!/usr/bin/env python3
"""
下载静态资源脚本
将外部CDN资源下载到本地，提高加载速度
"""

import os
import requests
import sys
from urllib.parse import urlparse

def download_file(url, local_path):
    """下载文件到本地"""
    try:
        print(f"正在下载: {url}")
        
        # 创建目录
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        # 下载文件
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        # 保存文件
        with open(local_path, 'wb') as f:
            f.write(response.content)
        
        print(f"下载完成: {local_path} ({len(response.content)} bytes)")
        return True
        
    except Exception as e:
        print(f"下载失败 {url}: {e}")
        return False

def main():
    """主函数"""
    print("=== 静态资源下载工具 ===")
    print("正在下载Bootstrap和其他必要资源...")
    print()
    
    # 要下载的资源列表
    resources = [
        {
            'url': 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
            'path': 'static/css/bootstrap.min.css'
        },
        {
            'url': 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css',
            'path': 'static/css/bootstrap-icons.css'
        },
        {
            'url': 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js',
            'path': 'static/js/bootstrap.bundle.min.js'
        },
        {
            'url': 'https://cdn.jsdelivr.net/npm/html5-qrcode@2.2.1/dist/html5-qrcode.min.js',
            'path': 'static/js/html5-qrcode.min.js'
        }
    ]
    
    # 下载字体文件（Bootstrap Icons需要）
    font_resources = [
        {
            'url': 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/fonts/bootstrap-icons.woff',
            'path': 'static/fonts/bootstrap-icons.woff'
        },
        {
            'url': 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/fonts/bootstrap-icons.woff2',
            'path': 'static/fonts/bootstrap-icons.woff2'
        }
    ]
    
    success_count = 0
    total_count = len(resources) + len(font_resources)
    
    # 下载主要资源
    for resource in resources:
        if download_file(resource['url'], resource['path']):
            success_count += 1
        print()
    
    # 下载字体资源
    print("下载字体文件...")
    for font in font_resources:
        if download_file(font['url'], font['path']):
            success_count += 1
        print()
    
    # 修复Bootstrap Icons CSS中的字体路径
    print("修复Bootstrap Icons字体路径...")
    try:
        css_file = 'static/css/bootstrap-icons.css'
        if os.path.exists(css_file):
            with open(css_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换字体路径
            content = content.replace(
                'url("./fonts/bootstrap-icons.woff2")',
                'url("../fonts/bootstrap-icons.woff2")'
            )
            content = content.replace(
                'url("./fonts/bootstrap-icons.woff")',
                'url("../fonts/bootstrap-icons.woff")'
            )
            
            with open(css_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("字体路径修复完成")
    except Exception as e:
        print(f"修复字体路径失败: {e}")
    
    print(f"\n=== 下载完成 ===")
    print(f"成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("所有资源下载成功！")
        print("现在可以修改HTML模板使用本地资源了。")
    else:
        print("部分资源下载失败，请检查网络连接。")
    
    return 0 if success_count == total_count else 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n下载被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"发生错误: {e}")
        sys.exit(1)
