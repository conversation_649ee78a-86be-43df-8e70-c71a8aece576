# HTTPS配置指南

## 概述

为了解决内网设备访问显示"不安全"以及无法调用手机摄像头的问题，我们已经为称重系统配置了HTTPS支持。

## 问题说明

1. **不安全警告**：现代浏览器将HTTP连接标记为"不安全"
2. **摄像头访问限制**：浏览器出于安全考虑，只允许HTTPS网站访问摄像头（localhost除外）

## 解决方案

我们通过以下步骤解决了这些问题：

1. ✅ 生成了SSL自签名证书
2. ✅ 修改了Flask应用以支持HTTPS
3. ✅ 更新了前端代码以适配HTTPS环境
4. 📋 提供客户端证书安装指导（本文档）

## 启动HTTPS服务

### 方法1：直接运行Flask应用
```bash
python app.py
```

### 方法2：使用系统托盘应用
```bash
python tray_app.py
```

启动后，系统会自动检测SSL证书并使用HTTPS模式。

## 访问地址

- **HTTPS地址**：`https://您的服务器IP:5050`
- **本地访问**：`https://localhost:5050`

## 客户端证书安装指导

由于使用的是自签名证书，客户端设备需要手动信任此证书才能正常访问。

### Windows系统

#### 方法1：通过浏览器安装（推荐）

1. 在浏览器中访问 `https://您的服务器IP:5050`
2. 浏览器会显示"您的连接不是私密连接"警告
3. 点击"高级"或"详细信息"
4. 点击"继续前往 您的服务器IP（不安全）"
5. 在地址栏左侧点击"不安全"图标
6. 点击"证书"
7. 在证书窗口中点击"详细信息"选项卡
8. 点击"复制到文件"
9. 按向导保存证书文件
10. 双击保存的证书文件
11. 点击"安装证书"
12. 选择"本地计算机"，点击"下一步"
13. 选择"将所有的证书都放入下列存储"
14. 点击"浏览"，选择"受信任的根证书颁发机构"
15. 点击"确定"，然后"下一步"，最后"完成"

#### 方法2：直接安装证书文件

1. 将服务器上的 `ssl/server.crt` 文件复制到客户端
2. 双击 `server.crt` 文件
3. 点击"安装证书"
4. 选择"本地计算机"，点击"下一步"
5. 选择"将所有的证书都放入下列存储"
6. 点击"浏览"，选择"受信任的根证书颁发机构"
7. 点击"确定"，然后"下一步"，最后"完成"

### Android系统

#### 方法1：通过浏览器（Chrome）

1. 在Chrome中访问 `https://您的服务器IP:5050`
2. 点击"高级"
3. 点击"继续前往 您的服务器IP（不安全）"
4. 在地址栏点击"不安全"图标
5. 点击"证书"
6. 点击"详细信息"
7. 点击"导出"，保存证书文件

#### 方法2：通过系统设置安装

1. 将 `ssl/server.crt` 文件传输到Android设备
2. 打开"设置" > "安全" > "加密与凭据"
3. 点击"从存储设备安装"
4. 选择证书文件
5. 输入证书名称（如：称重系统证书）
6. 选择"VPN和应用"用途
7. 点击"确定"

### iOS系统

1. 将 `ssl/server.crt` 文件通过邮件或其他方式发送到iOS设备
2. 在iOS设备上打开证书文件
3. 点击"安装"
4. 输入设备密码
5. 点击"安装"确认
6. 进入"设置" > "通用" > "关于本机" > "证书信任设置"
7. 找到刚安装的证书，开启信任开关

### macOS系统

1. 双击 `ssl/server.crt` 文件
2. 在钥匙串访问中找到该证书
3. 双击证书
4. 展开"信任"部分
5. 将"使用此证书时"设置为"始终信任"
6. 关闭窗口并输入管理员密码

## 验证安装

证书安装完成后：

1. 重启浏览器
2. 访问 `https://您的服务器IP:5050`
3. 地址栏应显示锁形图标，表示连接安全
4. 扫码功能应能正常调用摄像头

## 常见问题

### Q: 为什么还是显示"不安全"？
A: 请确保：
- 证书已正确安装到"受信任的根证书颁发机构"
- 浏览器已重启
- 使用的是HTTPS地址而不是HTTP

### Q: 手机摄像头还是无法调用？
A: 请检查：
- 是否使用HTTPS访问
- 证书是否已在手机上安装并信任
- 浏览器是否已授予摄像头权限

### Q: 证书过期怎么办？
A: 重新运行 `python generate_ssl_cert.py` 生成新证书，然后重新安装。

### Q: 多台设备都需要安装证书吗？
A: 是的，每台需要访问系统的设备都需要安装并信任证书。

## 技术说明

- 证书有效期：365天
- 证书类型：RSA 2048位自签名证书
- 支持的域名/IP：localhost、服务器主机名、多个常用内网IP段
- 证书文件位置：`ssl/server.crt` 和 `ssl/server.key`

## 安全注意事项

1. 自签名证书仅适用于内网环境
2. 不要在公网环境使用自签名证书
3. 定期更新证书（建议每年更新一次）
4. 妥善保管私钥文件（`ssl/server.key`）

## 联系支持

如果在配置过程中遇到问题，请检查：
1. 服务器防火墙是否开放5050端口
2. 网络连接是否正常
3. 证书文件是否完整
