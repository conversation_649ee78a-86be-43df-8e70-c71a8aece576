<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>摄像头调试工具 - 称重系统</title>
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .debug-card {
            margin-bottom: 20px;
        }
        .status-good {
            color: #28a745;
        }
        .status-warning {
            color: #ffc107;
        }
        .status-error {
            color: #dc3545;
        }
        .debug-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .test-video {
            max-width: 100%;
            height: 200px;
            background: #000;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-camera-video me-2"></i>摄像头调试工具</h2>
                    <a href="/scan" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left me-1"></i>返回扫描页面
                    </a>
                </div>
            </div>
        </div>

        <!-- 系统检查 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card debug-card">
                    <div class="card-header">
                        <h5><i class="bi bi-gear me-2"></i>系统检查</h5>
                    </div>
                    <div class="card-body">
                        <div id="system-checks">
                            <div class="d-flex justify-content-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">检查中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card debug-card">
                    <div class="card-header">
                        <h5><i class="bi bi-camera me-2"></i>摄像头测试</h5>
                    </div>
                    <div class="card-body">
                        <video id="test-video" class="test-video mb-3" autoplay muted></video>
                        <div class="d-grid gap-2">
                            <button id="test-camera" class="btn btn-primary">
                                <i class="bi bi-play-circle me-1"></i>测试摄像头
                            </button>
                            <button id="stop-test" class="btn btn-secondary" disabled>
                                <i class="bi bi-stop-circle me-1"></i>停止测试
                            </button>
                        </div>
                        <div id="camera-status" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 调试日志 -->
        <div class="row">
            <div class="col-12">
                <div class="card debug-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-terminal me-2"></i>调试日志</h5>
                        <button id="clear-log" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-trash me-1"></i>清空日志
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="debug-log" class="debug-log"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 解决方案 -->
        <div class="row">
            <div class="col-12">
                <div class="card debug-card">
                    <div class="card-header">
                        <h5><i class="bi bi-lightbulb me-2"></i>常见问题解决方案</h5>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="solutionsAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#solution1">
                                        摄像头权限被拒绝
                                    </button>
                                </h2>
                                <div id="solution1" class="accordion-collapse collapse" data-bs-parent="#solutionsAccordion">
                                    <div class="accordion-body">
                                        <ol>
                                            <li>点击地址栏左侧的摄像头图标</li>
                                            <li>选择"允许"访问摄像头</li>
                                            <li>刷新页面重新尝试</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#solution2">
                                        需要HTTPS连接
                                    </button>
                                </h2>
                                <div id="solution2" class="accordion-collapse collapse" data-bs-parent="#solutionsAccordion">
                                    <div class="accordion-body">
                                        <ol>
                                            <li>确保使用HTTPS访问系统</li>
                                            <li>如果是自签名证书，需要信任证书</li>
                                            <li>联系管理员配置正确的HTTPS</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#solution3">
                                        摄像头被其他应用占用
                                    </button>
                                </h2>
                                <div id="solution3" class="accordion-collapse collapse" data-bs-parent="#solutionsAccordion">
                                    <div class="accordion-body">
                                        <ol>
                                            <li>关闭其他使用摄像头的应用（如微信、QQ、Zoom等）</li>
                                            <li>重启浏览器</li>
                                            <li>如果问题持续，重启设备</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script>
        let testStream = null;
        const debugLog = document.getElementById('debug-log');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'status-error' : type === 'warning' ? 'status-warning' : 'status-good';
            debugLog.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            debugLog.scrollTop = debugLog.scrollHeight;
        }
        
        function clearLog() {
            debugLog.innerHTML = '';
        }

        // 改进的Html5QrCode库检测函数
        function checkHtml5QrcodeLibrary() {
            try {
                log('🔍 开始检查Html5QrCode库...');

                // 检查多种可能的库引用方式
                let Html5QrcodeClass = null;
                let loadMethod = '';

                if (typeof Html5Qrcode !== 'undefined') {
                    Html5QrcodeClass = Html5Qrcode;
                    loadMethod = '全局Html5Qrcode变量';
                    log('✅ 找到全局Html5Qrcode变量');
                } else if (typeof window.Html5Qrcode !== 'undefined') {
                    Html5QrcodeClass = window.Html5Qrcode;
                    loadMethod = 'window.Html5Qrcode';
                    log('✅ 找到window.Html5Qrcode');
                } else if (typeof window.__Html5QrcodeLibrary__ !== 'undefined' && window.__Html5QrcodeLibrary__.Html5Qrcode) {
                    Html5QrcodeClass = window.__Html5QrcodeLibrary__.Html5Qrcode;
                    loadMethod = '__Html5QrcodeLibrary__对象';
                    log('✅ 找到__Html5QrcodeLibrary__.Html5Qrcode，设置全局引用');

                    // 设置全局引用
                    window.Html5Qrcode = Html5QrcodeClass;
                    window.Html5QrcodeSupportedFormats = window.__Html5QrcodeLibrary__.Html5QrcodeSupportedFormats;
                    window.Html5QrcodeScanType = window.__Html5QrcodeLibrary__.Html5QrcodeScanType;

                    // 尝试设置全局变量
                    try {
                        if (typeof Html5Qrcode === 'undefined') {
                            Html5Qrcode = Html5QrcodeClass;
                            Html5QrcodeSupportedFormats = window.__Html5QrcodeLibrary__.Html5QrcodeSupportedFormats;
                            Html5QrcodeScanType = window.__Html5QrcodeLibrary__.Html5QrcodeScanType;
                            log('✅ 全局变量设置成功');
                        }
                    } catch (e) {
                        log('⚠️ 无法设置全局变量: ' + e.message, 'warning');
                    }
                }

                if (!Html5QrcodeClass) {
                    log('❌ Html5Qrcode类未找到');
                    const availableKeys = Object.keys(window).filter(key =>
                        key.includes('Html5') || key.includes('QR') || key.includes('qr')
                    );
                    log('🔍 可用的相关全局对象: ' + availableKeys.join(', '));
                    return {
                        loaded: false,
                        message: 'Html5QrCode库未找到',
                        details: '未找到Html5Qrcode类'
                    };
                }

                // 检查关键方法
                const requiredMethods = ['start', 'stop', 'clear'];
                const prototype = Html5QrcodeClass.prototype;
                const missingMethods = [];

                for (const method of requiredMethods) {
                    if (typeof prototype[method] !== 'function') {
                        missingMethods.push(method);
                    }
                }

                if (missingMethods.length > 0) {
                    log('❌ 缺少必要方法: ' + missingMethods.join(', '), 'error');
                    return {
                        loaded: false,
                        message: '库不完整，缺少方法: ' + missingMethods.join(', '),
                        details: '方法检查失败'
                    };
                }

                log('✅ Html5QrCode库完整性检查通过');
                return {
                    loaded: true,
                    message: `已加载 (通过${loadMethod})`,
                    details: '所有必要方法都可用',
                    class: Html5QrcodeClass
                };

            } catch (error) {
                log('❌ 库检查过程中出错: ' + error.message, 'error');
                return {
                    loaded: false,
                    message: '检查过程出错: ' + error.message,
                    details: error.toString()
                };
            }
        }
        
        // 系统检查
        function performSystemChecks() {
            const checksContainer = document.getElementById('system-checks');
            let checks = [];
            
            // 检查浏览器支持
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                checks.push({ name: '浏览器支持', status: 'good', message: '支持摄像头API' });
                log('✅ 浏览器支持摄像头API');
            } else {
                checks.push({ name: '浏览器支持', status: 'error', message: '不支持摄像头API' });
                log('❌ 浏览器不支持摄像头API', 'error');
            }
            
            // 检查HTTPS
            if (location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
                checks.push({ name: 'HTTPS协议', status: 'good', message: '协议安全' });
                log('✅ 使用安全协议');
            } else {
                checks.push({ name: 'HTTPS协议', status: 'error', message: '需要HTTPS' });
                log('❌ 需要HTTPS协议', 'error');
            }
            
            // 检查Html5QrCode库（使用改进的检测逻辑）
            const libraryStatus = checkHtml5QrcodeLibrary();
            if (libraryStatus.loaded) {
                checks.push({ name: '扫描器库', status: 'good', message: libraryStatus.message });
                log('✅ ' + libraryStatus.message);
            } else {
                checks.push({ name: '扫描器库', status: 'warning', message: libraryStatus.message });
                log('⚠️ ' + libraryStatus.message, 'warning');
            }
            
            // 渲染检查结果
            checksContainer.innerHTML = checks.map(check => `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>${check.name}</span>
                    <span class="status-${check.status}">
                        <i class="bi bi-${check.status === 'good' ? 'check-circle' : check.status === 'warning' ? 'exclamation-triangle' : 'x-circle'}"></i>
                        ${check.message}
                    </span>
                </div>
            `).join('');
        }
        
        // 测试摄像头
        document.getElementById('test-camera').addEventListener('click', async function() {
            const video = document.getElementById('test-video');
            const status = document.getElementById('camera-status');
            const testBtn = this;
            const stopBtn = document.getElementById('stop-test');
            
            testBtn.disabled = true;
            testBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>测试中...';
            
            try {
                log('🎬 开始测试摄像头...');
                testStream = await navigator.mediaDevices.getUserMedia({ video: true });
                video.srcObject = testStream;
                
                status.innerHTML = '<div class="alert alert-success"><i class="bi bi-check-circle me-2"></i>摄像头工作正常！</div>';
                log('✅ 摄像头测试成功');
                
                stopBtn.disabled = false;
                testBtn.innerHTML = '<i class="bi bi-check-circle me-1"></i>测试成功';
                
            } catch (error) {
                log(`❌ 摄像头测试失败: ${error.message}`, 'error');
                
                let errorMessage = '摄像头测试失败';
                if (error.name === 'NotAllowedError') {
                    errorMessage = '权限被拒绝，请允许访问摄像头';
                } else if (error.name === 'NotFoundError') {
                    errorMessage = '未找到摄像头设备';
                } else if (error.name === 'NotReadableError') {
                    errorMessage = '摄像头被其他应用占用';
                }
                
                status.innerHTML = `<div class="alert alert-danger"><i class="bi bi-x-circle me-2"></i>${errorMessage}</div>`;
                testBtn.innerHTML = '<i class="bi bi-play-circle me-1"></i>重新测试';
                testBtn.disabled = false;
            }
        });
        
        // 停止测试
        document.getElementById('stop-test').addEventListener('click', function() {
            if (testStream) {
                testStream.getTracks().forEach(track => track.stop());
                testStream = null;
                document.getElementById('test-video').srcObject = null;
                document.getElementById('camera-status').innerHTML = '';
                
                this.disabled = true;
                document.getElementById('test-camera').innerHTML = '<i class="bi bi-play-circle me-1"></i>测试摄像头';
                document.getElementById('test-camera').disabled = false;
                
                log('🛑 摄像头测试已停止');
            }
        });
        
        // 清空日志
        document.getElementById('clear-log').addEventListener('click', clearLog);
        
        // 页面加载时执行系统检查
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 摄像头调试工具启动');
            performSystemChecks();
        });
    </script>
</body>
</html>
