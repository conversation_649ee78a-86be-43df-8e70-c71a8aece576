#!/usr/bin/env python3
"""
SSL证书生成脚本
为称重系统生成自签名SSL证书，用于HTTPS访问
"""

import os
import subprocess
import sys
from datetime import datetime, <PERSON>el<PERSON>

def check_openssl():
    """检查OpenSSL是否可用"""
    try:
        result = subprocess.run(['openssl', 'version'], 
                              capture_output=True, text=True, check=True)
        print(f"OpenSSL版本: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def generate_certificate():
    """生成SSL证书"""
    # 创建ssl目录
    ssl_dir = 'ssl'
    os.makedirs(ssl_dir, exist_ok=True)
    
    # 证书文件路径
    key_file = os.path.join(ssl_dir, 'server.key')
    cert_file = os.path.join(ssl_dir, 'server.crt')
    
    # 如果证书已存在，询问是否重新生成
    if os.path.exists(cert_file) and os.path.exists(key_file):
        response = input("SSL证书已存在，是否重新生成？(y/N): ")
        if response.lower() != 'y':
            print("使用现有证书")
            return key_file, cert_file
    
    print("正在生成SSL证书...")
    
    # 获取本机IP地址（用于证书的Subject Alternative Name）
    try:
        import socket
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        print(f"检测到本机IP: {local_ip}")
    except:
        local_ip = "*************"  # 默认IP
        print(f"使用默认IP: {local_ip}")
    
    # 创建配置文件
    config_content = f"""[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = CN
ST = Beijing
L = Beijing
O = WeighingSystem
OU = IT Department
CN = {local_ip}

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = {hostname}
IP.1 = 127.0.0.1
IP.2 = {local_ip}
IP.3 = *************
IP.4 = *************
IP.5 = **********
"""
    
    config_file = os.path.join(ssl_dir, 'openssl.conf')
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    try:
        # 生成私钥
        print("生成私钥...")
        subprocess.run([
            'openssl', 'genrsa', '-out', key_file, '2048'
        ], check=True)
        
        # 生成证书
        print("生成证书...")
        subprocess.run([
            'openssl', 'req', '-new', '-x509', '-key', key_file,
            '-out', cert_file, '-days', '365',
            '-config', config_file, '-extensions', 'v3_req'
        ], check=True)
        
        print(f"SSL证书生成成功!")
        print(f"私钥文件: {key_file}")
        print(f"证书文件: {cert_file}")
        
        # 显示证书信息
        print("\n证书信息:")
        subprocess.run([
            'openssl', 'x509', '-in', cert_file, '-text', '-noout'
        ])
        
        return key_file, cert_file
        
    except subprocess.CalledProcessError as e:
        print(f"生成证书失败: {e}")
        return None, None
    finally:
        # 清理配置文件
        if os.path.exists(config_file):
            os.remove(config_file)

def generate_with_python():
    """使用Python的cryptography库生成证书"""
    try:
        from cryptography import x509
        from cryptography.x509.oid import NameOID
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.asymmetric import rsa
        from cryptography.hazmat.primitives import serialization
        import ipaddress
        import socket
    except ImportError:
        print("需要安装cryptography库: pip install cryptography")
        return None, None
    
    # 创建ssl目录
    ssl_dir = 'ssl'
    os.makedirs(ssl_dir, exist_ok=True)
    
    key_file = os.path.join(ssl_dir, 'server.key')
    cert_file = os.path.join(ssl_dir, 'server.crt')
    
    print("使用Python生成SSL证书...")
    
    # 生成私钥
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
    )
    
    # 获取本机信息
    try:
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
    except:
        hostname = "localhost"
        local_ip = "127.0.0.1"
    
    # 创建证书
    subject = issuer = x509.Name([
        x509.NameAttribute(NameOID.COUNTRY_NAME, "CN"),
        x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Beijing"),
        x509.NameAttribute(NameOID.LOCALITY_NAME, "Beijing"),
        x509.NameAttribute(NameOID.ORGANIZATION_NAME, "WeighingSystem"),
        x509.NameAttribute(NameOID.COMMON_NAME, local_ip),
    ])
    
    # 添加Subject Alternative Names
    san_list = [
        x509.DNSName("localhost"),
        x509.DNSName(hostname),
        x509.IPAddress(ipaddress.IPv4Address("127.0.0.1")),
        x509.IPAddress(ipaddress.IPv4Address(local_ip)),
    ]
    
    cert = x509.CertificateBuilder().subject_name(
        subject
    ).issuer_name(
        issuer
    ).public_key(
        private_key.public_key()
    ).serial_number(
        x509.random_serial_number()
    ).not_valid_before(
        datetime.utcnow()
    ).not_valid_after(
        datetime.utcnow() + timedelta(days=365)
    ).add_extension(
        x509.SubjectAlternativeName(san_list),
        critical=False,
    ).sign(private_key, hashes.SHA256())
    
    # 保存私钥
    with open(key_file, "wb") as f:
        f.write(private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        ))
    
    # 保存证书
    with open(cert_file, "wb") as f:
        f.write(cert.public_bytes(serialization.Encoding.PEM))
    
    print(f"SSL证书生成成功!")
    print(f"私钥文件: {key_file}")
    print(f"证书文件: {cert_file}")
    print(f"证书包含的域名/IP: localhost, {hostname}, 127.0.0.1, {local_ip}")
    
    return key_file, cert_file

def main():
    print("=== SSL证书生成工具 ===")
    print("为称重系统生成HTTPS证书")
    print()
    
    # 检查OpenSSL
    if check_openssl():
        print("使用OpenSSL生成证书...")
        key_file, cert_file = generate_certificate()
    else:
        print("OpenSSL不可用，尝试使用Python生成证书...")
        key_file, cert_file = generate_with_python()
    
    if key_file and cert_file:
        print("\n=== 证书生成完成 ===")
        print("接下来需要:")
        print("1. 修改应用程序以使用HTTPS")
        print("2. 在客户端设备上安装并信任此证书")
        print("3. 使用 https://您的IP:5050 访问系统")
        print()
        print("注意: 这是自签名证书，浏览器会显示安全警告")
        print("需要在每个客户端设备上手动信任此证书")
    else:
        print("证书生成失败!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
