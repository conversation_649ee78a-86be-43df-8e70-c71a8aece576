<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码扫描测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
    <style>
        .scanner-container {
            max-width: 500px;
            margin: 0 auto;
            border: 2px solid #0d6efd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        #scanner-preview {
            width: 100%;
            height: 400px;
        }
        
        .result-container {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .log-container {
            background-color: #212529;
            color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <h2 class="text-center mb-4">
                    <i class="bi bi-qr-code-scan me-2"></i>二维码扫描测试
                </h2>
                
                <!-- 扫描器区域 -->
                <div class="scanner-container mb-3">
                    <div id="scanner-preview"></div>
                </div>
                
                <!-- 控制按钮 -->
                <div class="text-center mb-3">
                    <button id="start-scan" class="btn btn-primary me-2">
                        <i class="bi bi-camera-video me-1"></i>启动扫描
                    </button>
                    <button id="stop-scan" class="btn btn-secondary" disabled>
                        <i class="bi bi-stop-circle me-1"></i>停止扫描
                    </button>
                </div>
                
                <!-- 摄像头选择 -->
                <div class="mb-3">
                    <label for="camera-select" class="form-label">选择摄像头</label>
                    <select class="form-select" id="camera-select">
                        <option value="">正在获取摄像头列表...</option>
                    </select>
                </div>
                
                <!-- 扫描结果 -->
                <div id="result-container" class="result-container" style="display: none;">
                    <h5><i class="bi bi-check-circle-fill text-success me-2"></i>扫描结果</h5>
                    <div id="scan-result"></div>
                </div>
                
                <!-- 调试日志 -->
                <div class="mt-4">
                    <h6><i class="bi bi-terminal me-2"></i>调试日志</h6>
                    <div id="log-output" class="log-container">
                        等待操作...
                    </div>
                    <button id="clear-log" class="btn btn-outline-secondary btn-sm mt-2">清空日志</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let html5QrCode = null;
        let scanning = false;
        let availableCameras = [];
        let selectedCameraId = null;

        const startBtn = document.getElementById('start-scan');
        const stopBtn = document.getElementById('stop-scan');
        const cameraSelect = document.getElementById('camera-select');
        const resultContainer = document.getElementById('result-container');
        const scanResult = document.getElementById('scan-result');
        const logOutput = document.getElementById('log-output');

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            console.log(logEntry);
            
            const logDiv = document.createElement('div');
            logDiv.textContent = logEntry;
            if (type === 'error') logDiv.style.color = '#ff6b6b';
            if (type === 'success') logDiv.style.color = '#51cf66';
            if (type === 'warning') logDiv.style.color = '#ffd43b';
            
            logOutput.appendChild(logDiv);
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        // 清空日志
        document.getElementById('clear-log').addEventListener('click', () => {
            logOutput.innerHTML = '';
        });

        // 获取摄像头设备
        async function getCameraDevices() {
            try {
                log('正在获取摄像头设备列表...');
                
                // 首先请求摄像头权限
                await navigator.mediaDevices.getUserMedia({ video: true });
                
                // 获取所有媒体设备
                const devices = await navigator.mediaDevices.enumerateDevices();
                const videoDevices = devices.filter(device => device.kind === 'videoinput');
                
                log(`发现 ${videoDevices.length} 个摄像头设备`, 'success');
                
                availableCameras = videoDevices.map((device, index) => ({
                    deviceId: device.deviceId,
                    label: device.label || `摄像头 ${index + 1}`,
                    groupId: device.groupId
                }));
                
                // 更新摄像头选择下拉框
                updateCameraSelect();
                
                return availableCameras;
            } catch (error) {
                log(`获取摄像头设备失败: ${error.message}`, 'error');
                return [];
            }
        }

        // 更新摄像头选择下拉框
        function updateCameraSelect() {
            cameraSelect.innerHTML = '';
            
            if (availableCameras.length === 0) {
                cameraSelect.innerHTML = '<option value="">未找到摄像头设备</option>';
                return;
            }
            
            // 添加默认选项
            cameraSelect.innerHTML = '<option value="">选择摄像头...</option>';
            
            // 添加摄像头选项
            availableCameras.forEach((camera, index) => {
                const option = document.createElement('option');
                option.value = camera.deviceId;
                option.textContent = camera.label;
                
                // 默认选择后置摄像头
                if (camera.label.toLowerCase().includes('back') || 
                    camera.label.toLowerCase().includes('rear') || 
                    camera.label.toLowerCase().includes('environment') ||
                    index === availableCameras.length - 1) {
                    option.selected = true;
                    selectedCameraId = camera.deviceId;
                }
                
                cameraSelect.appendChild(option);
            });
            
            // 添加摄像头切换事件监听器
            cameraSelect.addEventListener('change', function() {
                selectedCameraId = this.value;
                log(`选择摄像头: ${this.options[this.selectedIndex].text}`);
                
                if (selectedCameraId && scanning) {
                    log('正在切换摄像头...');
                    restartScanner();
                }
            });
        }

        // 重启扫描器
        function restartScanner() {
            if (html5QrCode && scanning) {
                html5QrCode.stop().then(() => {
                    setTimeout(() => {
                        startScanning();
                    }, 500);
                }).catch(error => {
                    log(`停止扫描器失败: ${error.message}`, 'error');
                });
            }
        }

        // 启动扫描
        function startScanning() {
            if (!selectedCameraId) {
                log('请先选择摄像头设备', 'warning');
                return;
            }

            const config = {
                fps: 10,
                qrbox: { width: 250, height: 250 },
                aspectRatio: 1.0,
                formatsToSupport: [Html5QrcodeSupportedFormats.QR_CODE],
                videoConstraints: {
                    deviceId: { exact: selectedCameraId },
                    width: { ideal: 1280 },
                    height: { ideal: 720 },
                    focusMode: "continuous",
                    focusDistance: { ideal: 0.1 },
                    advanced: [
                        { focusMode: "continuous" },
                        { focusDistance: { min: 0, max: 1, ideal: 0.1 } }
                    ]
                }
            };

            html5QrCode.start(
                { deviceId: { exact: selectedCameraId } },
                config,
                (decodedText) => {
                    log(`🎉 扫描成功! 内容: "${decodedText}"`, 'success');
                    log(`内容类型: ${typeof decodedText}`, 'info');
                    log(`内容长度: ${decodedText ? decodedText.length : 0}`, 'info');
                    
                    // 显示扫描结果
                    showScanResult(decodedText);
                    
                    // 停止扫描
                    stopScanning();
                },
                (errorMessage) => {
                    // 扫描过程中的错误（通常是没有检测到二维码）
                    // console.log('扫描过程中:', errorMessage);
                }
            ).then(() => {
                log('扫描器启动成功', 'success');
                scanning = true;
                startBtn.disabled = true;
                stopBtn.disabled = false;
            }).catch(error => {
                log(`扫描器启动失败: ${error.message}`, 'error');
            });
        }

        // 停止扫描
        function stopScanning() {
            if (html5QrCode && scanning) {
                html5QrCode.stop().then(() => {
                    log('扫描器已停止');
                    scanning = false;
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                }).catch(error => {
                    log(`停止扫描器失败: ${error.message}`, 'error');
                });
            }
        }

        // 显示扫描结果
        function showScanResult(result) {
            scanResult.innerHTML = `
                <div class="alert alert-success">
                    <strong>扫描内容:</strong> ${result}<br>
                    <strong>内容长度:</strong> ${result.length} 字符<br>
                    <strong>内容类型:</strong> ${typeof result}
                </div>
            `;
            resultContainer.style.display = 'block';
        }

        // 启动扫描按钮
        startBtn.addEventListener('click', () => {
            if (!html5QrCode) {
                html5QrCode = new Html5Qrcode("scanner-preview");
            }
            startScanning();
        });

        // 停止扫描按钮
        stopBtn.addEventListener('click', stopScanning);

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('二维码扫描测试页面已加载');
            getCameraDevices();
        });
    </script>
</body>
</html>
