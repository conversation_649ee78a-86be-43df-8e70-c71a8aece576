<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信用户帮助 - 称重管理系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .icon {
            text-align: center;
            font-size: 60px;
            margin-bottom: 20px;
        }
        .alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #ffc107;
        }
        .step {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }
        .step-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #007bff;
        }
        .url-box {
            background: #e9ecef;
            border: 2px dashed #6c757d;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            margin: 15px 0;
            font-family: monospace;
            word-break: break-all;
            font-size: 14px;
        }
        .btn {
            display: block;
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 10px;
            border: none;
            font-size: 16px;
            cursor: pointer;
        }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📱</div>
        <h1>称重管理系统</h1>
        <h2 style="text-align: center; color: #666; margin-bottom: 20px;">微信用户使用指南</h2>
        
        <div class="alert">
            <strong>⚠️ 重要提示：</strong><br>
            微信浏览器限制了摄像头功能，无法使用扫码称重。<br>
            请按以下步骤在手机浏览器中打开。
        </div>
        
        <div class="step">
            <div class="step-title">步骤 1</div>
            点击微信右上角的"..."菜单按钮
        </div>
        
        <div class="step">
            <div class="step-title">步骤 2</div>
            选择"在浏览器中打开"或"用Safari打开"
        </div>
        
        <div class="step">
            <div class="step-title">步骤 3</div>
            在浏览器中允许网站访问摄像头权限
        </div>
        
        <h3 style="margin: 20px 0 10px 0;">📋 手动复制链接：</h3>
        <div class="url-box" id="url-display">
            正在获取网址...
        </div>
        
        <button class="btn btn-success" onclick="copyUrl()">📋 复制链接</button>
        <a href="../wechat-simple" class="btn">🔧 使用简化版功能</a>
        
        <div class="footer">
            <p>推荐浏览器：Safari (iOS) | Chrome (Android)</p>
            <p>如有问题请联系系统管理员</p>
        </div>
    </div>

    <script>
        // 获取并显示当前网址
        function updateUrl() {
            const url = window.location.origin;
            document.getElementById('url-display').textContent = url;
        }
        
        // 复制网址到剪贴板
        function copyUrl() {
            const url = window.location.origin;
            
            // 尝试使用现代API
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(url).then(function() {
                    alert('✅ 链接已复制到剪贴板！\n请在浏览器中粘贴访问。');
                }).catch(function() {
                    fallbackCopy(url);
                });
            } else {
                fallbackCopy(url);
            }
        }
        
        // 备用复制方法
        function fallbackCopy(text) {
            // 创建临时文本框
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    alert('✅ 链接已复制！\n请在浏览器中粘贴访问：\n' + text);
                } else {
                    showManualCopy(text);
                }
            } catch (err) {
                showManualCopy(text);
            }
            
            document.body.removeChild(textArea);
        }
        
        // 显示手动复制提示
        function showManualCopy(text) {
            alert('📋 请手动复制以下链接在浏览器中打开：\n\n' + text + '\n\n提示：长按链接可以复制');
        }
        
        // 检测浏览器环境
        function detectEnvironment() {
            const ua = navigator.userAgent;
            const isWeChat = /MicroMessenger/i.test(ua);
            const isiOS = /iPad|iPhone|iPod/.test(ua);
            const isAndroid = /Android/i.test(ua);
            
            console.log('浏览器环境检测:', {
                userAgent: ua,
                isWeChat: isWeChat,
                isiOS: isiOS,
                isAndroid: isAndroid
            });
            
            // 如果不在微信中，提示用户
            if (!isWeChat) {
                setTimeout(function() {
                    if (confirm('检测到您可能不在微信浏览器中。\n是否直接访问系统首页？')) {
                        window.location.href = '../';
                    }
                }, 2000);
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            updateUrl();
            detectEnvironment();
        });
        
        // 防止页面被缓存
        window.addEventListener('pageshow', function(event) {
            if (event.persisted) {
                window.location.reload();
            }
        });
    </script>
</body>
</html>
