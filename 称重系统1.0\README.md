# 称重管理系统

## 系统概述

称重管理系统是一个专为产品称重管理设计的Web应用，通过扫描产品二维码快速获取产品信息并记录重量数据，实现产品编号、重量信息和数据同步的高效管理。系统采用Flask框架开发，支持在局域网内部署使用，适用于需要管理产品重量信息的生产环境。

## 功能特点

### 1. 扫码称重
- 支持扫描产品二维码快速获取产品信息
- 自动匹配产品编号与产品代码和规格信息
- 记录产品重量数据（卷重和最终重量）
- 实时更新产品重量信息

### 2. 数据同步
- 自动同步相同产品代码的重量信息
- 确保产品数据的一致性和准确性
- 支持数据导入导出功能

### 3. 编号段管理
- 管理产品编号段与产品代码、规格信息的对应关系
- 支持添加、删除编号段
- 支持批量导入编号段数据

### 4. 产品信息管理
- 查看所有产品信息和重量数据
- 支持导出产品数据为Excel文件
- 自动记录数据更新时间

## 技术架构

- **后端框架**：Flask (Python)
- **数据存储**：Excel文件 (pandas)
- **前端技术**：HTML, CSS, JavaScript, Bootstrap 5
- **数据格式**：JSON, Excel

## 系统结构

```
称重系统/
├── app.py                # 主应用程序
├── database/             # 数据存储目录
│   ├── product_ranges.xlsx  # 产品编号段数据
│   └── products.xlsx        # 产品信息数据
├── static/               # 静态资源
│   └── css/
│       └── style.css     # 自定义样式
├── templates/            # HTML模板
│   ├── admin.html           # 管理后台首页
│   ├── admin_products.html  # 产品信息管理
│   ├── admin_ranges.html    # 编号段管理
│   ├── index.html           # 系统首页
│   └── scan.html            # 扫码称重页面
└── uploads/              # 上传文件目录
```

## 安装指南

### 环境要求
- Python 3.6+
- pandas
- Flask

### 安装步骤

1. 克隆或下载项目到本地

2. 安装依赖包
   ```
   pip install flask pandas openpyxl
   ```

3. 运行应用
   ```
   python app.py
   ```

4. 访问系统
   - 在浏览器中访问 `http://localhost:5050` 或 `http://[服务器IP]:5050`

## 使用说明

### 扫码称重
1. 点击导航栏的"扫码称重"进入扫码页面
2. 使用摄像头扫描产品二维码或手动输入产品编号
3. 系统自动匹配产品信息并显示
4. 输入卷重和最终重量，点击保存

### 编号段管理
1. 点击导航栏的"管理后台"，进入后台管理
2. 选择"编号段管理