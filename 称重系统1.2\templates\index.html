<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 称重管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap-icons.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">称重管理系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/scan">扫码称重</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/mobile-compatibility">兼容性检测</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin">管理后台</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8 text-center mb-5">
                <h1 class="mb-3">称重管理系统</h1>
                <p class="lead">高效管理产品编号、重量信息和数据同步</p>
            </div>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-qr-code-scan me-2"></i>扫码称重</h5>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <p>通过扫描产品二维码快速获取产品信息并记录重量数据</p>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>自动识别产品编号</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>快速录入重量信息</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>自动同步相同产品代码数据</li>
                                </ul>
                                <a href="/scan" class="btn btn-primary mt-auto">开始扫码</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-search me-2"></i>手动查询</h5>
                            </div>
                            <div class="card-body">
                                <p>通过产品编号手动查询产品信息和重量数据</p>
                                <form id="search-form" class="mt-3">
                                    <div class="mb-3">
                                        <label for="product-id" class="form-label">产品编号</label>
                                        <input type="text" class="form-control" id="product-id" placeholder="请输入产品编号" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">查询产品</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-2">
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-gear me-2"></i>管理后台</h5>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <p>管理产品编号段和查看所有产品信息</p>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>编号段管理</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>产品信息查看</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>数据导入导出</li>
                                </ul>
                                <a href="/admin" class="btn btn-primary mt-auto">进入管理</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-phone me-2"></i>移动端支持</h5>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <p>系统已全面优化移动端和微信浏览器兼容性</p>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>支持微信内置浏览器</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>优化iOS Safari体验</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>Android设备完美兼容</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>自动适配屏幕尺寸</li>
                                </ul>
                                <a href="/mobile-compatibility" class="btn btn-primary mt-auto">兼容性检测</a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-2">
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>系统信息</h5>
                            </div>
                            <div class="card-body">
                                <p>称重管理系统提供全面的产品重量管理解决方案</p>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>自动匹配产品编号和产品代码</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>记录线圈重量和成品重量</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>同步相同产品代码的重量数据</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>支持数据导入导出</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/wechat-redirect.js') }}"></script>
    <script>
        // 检测微信浏览器
        function isWeChatBrowser() {
            return /MicroMessenger/i.test(navigator.userAgent);
        }

        // 页面加载完成后检测环境
        document.addEventListener('DOMContentLoaded', function() {
            if (isWeChatBrowser()) {
                // 为微信用户显示特殊提示
                const container = document.querySelector('.container');
                const wechatAlert = document.createElement('div');
                wechatAlert.className = 'alert alert-info alert-dismissible fade show';
                wechatAlert.innerHTML = `
                    <i class="bi bi-wechat me-2"></i>
                    <strong>微信用户专享：</strong>
                    检测到您使用微信浏览器，我们为您准备了专门优化的版本。
                    <div class="mt-2">
                        <a href="/wechat-simple" class="btn btn-sm btn-outline-primary me-2">微信专用版</a>
                        <small class="text-muted">或点击右上角"..."在浏览器中打开获得完整功能</small>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                container.insertBefore(wechatAlert, container.firstElementChild);
            }
        });

        document.getElementById('search-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const productId = document.getElementById('product-id').value.trim();
            if (productId) {
                window.location.href = '/product/' + productId;
            }
        });
    </script>
</body>
</html>