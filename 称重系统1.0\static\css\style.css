/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.4rem;
}

/* 卡片样式 */
.card {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: none;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 20px;
}

.card-header {
    font-weight: 600;
    padding: 12px 20px;
}

.card-body {
    padding: 20px;
}

/* 按钮样式 */
.btn {
    border-radius: 5px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* 表单样式 */
.form-label {
    font-weight: 500;
    margin-bottom: 6px;
}

.form-control {
    border-radius: 5px;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

.table td, .table th {
    padding: 12px 15px;
    vertical-align: middle;
}

/* 扫码页面样式 */
.scanner-container {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    position: relative;
}

#scanner-preview {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #0d6efd;
}

.product-info {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.weight-input {
    font-size: 1.2rem;
    font-weight: 500;
    padding: 12px 15px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-body {
        padding: 15px;
    }
    
    .table td, .table th {
        padding: 8px 10px;
    }
}