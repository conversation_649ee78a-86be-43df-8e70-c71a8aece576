<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扫码称重 - 称重管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        #scanner-preview {
            width: 100%;
            height: 300px;
            position: relative;
            border: 2px solid #0d6efd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        #video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .scanner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 10;
        }
        
        .scan-region {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 200px;
            height: 200px;
            transform: translate(-50%, -50%);
            border: 2px solid #0d6efd;
            border-radius: 10px;
            box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
            z-index: 5;
        }
        
        .scan-region::before, .scan-region::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border-color: #0d6efd;
            border-style: solid;
        }
        
        .scan-region::before {
            top: -2px;
            left: -2px;
            border-width: 2px 0 0 2px;
            border-radius: 5px 0 0 0;
        }
        
        .scan-region::after {
            bottom: -2px;
            right: -2px;
            border-width: 0 2px 2px 0;
            border-radius: 0 0 5px 0;
        }
        
        .scan-line {
            position: absolute;
            width: 100%;
            height: 2px;
            background-color: #0d6efd;
            top: 50%;
            animation: scan 2s linear infinite;
        }
        
        @keyframes scan {
            0% { top: 20%; }
            50% { top: 80%; }
            100% { top: 20%; }
        }
        
        .product-card {
            transition: all 0.3s ease;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        
        .status-success {
            background-color: #28a745;
        }
        
        .status-pending {
            background-color: #ffc107;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            visibility: hidden;
            opacity: 0;
            transition: visibility 0s, opacity 0.3s;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #0d6efd;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">称重管理系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/scan">扫码称重</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin">管理后台</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8 text-center mb-4">
                <h2 class="mb-3">扫码称重</h2>
                <p class="lead">扫描产品二维码快速获取产品信息并记录重量数据</p>
            </div>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-qr-code-scan me-2"></i>扫描二维码</h5>
                        <div id="camera-status" class="d-flex align-items-center">
                            <span class="status-indicator status-pending" id="status-indicator"></span>
                            <small id="status-text">未启动</small>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="scanner-container mb-3">
                            <div id="scanner-preview" class="mb-3">
                                <video id="video" style="display: none;"></video>
                                <div class="scanner-overlay" id="scanner-overlay">
                                    <i class="bi bi-camera-video-off" style="font-size: 2rem;"></i>
                                    <p class="mt-2">点击下方按钮启动摄像头</p>
                                </div>
                                <div class="scan-region" style="display: none;">
                                    <div class="scan-line"></div>
                                </div>
                            </div>
                            <div class="d-flex justify-content-center">
                                <button id="start-scan" class="btn btn-primary me-2">
                                    <i class="bi bi-camera-video me-1"></i> 启动摄像头
                                </button>
                                <button id="stop-scan" class="btn btn-secondary" disabled>
                                    <i class="bi bi-stop-circle me-1"></i> 停止扫描
                                </button>
                            </div>
                        </div>
                        <div class="text-center mt-4">
                            <p class="text-muted">或者手动输入产品编号</p>
                            <form id="manual-form" class="mt-2">
                                <div class="input-group mb-3">
                                    <input type="text" id="manual-product-id" class="form-control" placeholder="请输入产品编号" required>
                                    <button class="btn btn-primary" type="submit">
                                        <i class="bi bi-search me-1"></i> 查询
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div id="product-info" class="card mb-4 product-card" style="display: none;">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>产品信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success" role="alert" id="product-found-alert">
                            <i class="bi bi-check-circle-fill me-2"></i> 已成功匹配产品信息
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p><strong>产品编号:</strong> <span id="product-uid"></span></p>
                                <p><strong>产品代码:</strong> <span id="product-code"></span></p>
                                <p><strong>型号规格:</strong> <span id="product-spec"></span></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>特殊要求:</strong> <span id="product-req"></span></p>
                                <p><strong>客户信息:</strong> <span id="product-customer"></span></p>
                                <p><strong>更新时间:</strong> <span id="product-time"></span></p>
                            </div>
                        </div>
                        
                        <form id="weight-form" class="mt-4">
                            <input type="hidden" id="product-id-input">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="coil-weight" class="form-label">线圈重量 (kg)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="coil-weight" step="0.01" min="0">
                                        <span class="input-group-text">kg</span>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="final-weight" class="form-label">成品重量 (kg)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="final-weight" step="0.01" min="0">
                                        <span class="input-group-text">kg</span>
                                    </div>
                                </div>
                            </div>
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-save me-1"></i> 保存重量信息
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div id="scan-instructions" class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="bi bi-question-circle me-2"></i>使用说明</h5>
                    </div>
                    <div class="card-body">
                        <ol class="mb-0">
                            <li class="mb-2">点击"启动摄像头"按钮开始扫描</li>
                            <li class="mb-2">将产品二维码对准扫描框内</li>
                            <li class="mb-2">系统自动识别产品编号并查询信息</li>
                            <li class="mb-2">输入线圈重量和成品重量</li>
                            <li class="mb-2">点击"保存重量信息"按钮提交数据</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 加载中遮罩 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
    </div>
    
    <!-- 提示弹窗 -->
    <div class="modal fade" id="alert-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="alert-title">提示</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="alert-message">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">确定</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 添加多个备用CDN链接，提高库加载成功率 -->
    <script>
        // 定义多个CDN源，按优先级排序（本地文件优先）
        const cdnSources = [
            "{{ url_for('static', filename='js/html5-qrcode.min.js') }}", // 本地文件优先加载
            "https://cdn.jsdelivr.net/npm/html5-qrcode@2.2.1/dist/html5-qrcode.min.js",
            "https://unpkg.com/html5-qrcode@2.2.1/dist/html5-qrcode.min.js",
            "https://cdnjs.cloudflare.com/ajax/libs/html5-qrcode/2.2.1/html5-qrcode.min.js",
            "https://cdn.staticfile.org/html5-qrcode/2.2.1/html5-qrcode.min.js"
        ];
        
        // 库加载状态
        let libraryLoaded = false;
        let loadingInProgress = false;
        
        // 尝试从下一个CDN加载
        function loadFromNextCDN(index) {
            if (loadingInProgress) {
                console.log('已有加载过程在进行中，跳过此次加载请求');
                return;
            }
            
            loadingInProgress = true;
            
            if (index >= cdnSources.length) {
                console.error('所有CDN源和本地回退文件都加载失败');
                showAlert('扫描器库加载失败', '所有加载源都失败。请尝试刷新页面或联系管理员。', true);
                loadingInProgress = false;
                return;
            }
            
            console.log(`尝试从源 ${index + 1}/${cdnSources.length} 加载...`);
            const script = document.createElement('script');
            script.src = cdnSources[index];
            
            // 添加缓存破坏参数，避免加载缓存的错误版本
            if (script.src.indexOf('?') === -1) {
                script.src = script.src + '?v=' + new Date().getTime();
            } else {
                script.src = script.src + '&v=' + new Date().getTime();
            }
            
            // 设置正确的MIME类型，避免某些浏览器的解析问题
            script.type = 'text/javascript';
            
            // 减少超时时间，加快加载过程
            const timeoutMs = index === 0 ? 1000 : 3000; // 本地文件1秒超时，CDN 3秒超时，增加超时时间以提高成功率
            const timeoutId = setTimeout(() => {
                console.warn(`源 ${index + 1} 加载超时`);
                if (!libraryLoaded) {
                    loadingInProgress = false;
                    loadFromNextCDN(index + 1);
                }
            }, timeoutMs);
            
            script.onload = function() {
                clearTimeout(timeoutId);
                console.log(`从源 ${index + 1} 加载成功`);
                // 验证库是否真正可用
                if (typeof Html5Qrcode !== 'undefined') {
                    console.log('HTML5Qrcode库验证成功');
                    libraryLoaded = true;
                    loadingInProgress = false;
                    
                    // 如果用户已经点击了启动按钮但因库未加载而失败，可以自动重试
                    const startScanBtn = document.getElementById('start-scan');
                    if (startScanBtn && startScanBtn.disabled) {
                        console.log('检测到之前的扫描尝试，自动重试...');
                        setTimeout(() => {
                            startScanBtn.disabled = false;
                            startScanBtn.click();
                        }, 500); // 增加延迟时间，确保库完全初始化
                    }
                } else {
                    console.error(`源 ${index + 1} 加载成功但库不可用，尝试下一个源`);
                    loadingInProgress = false;
                    loadFromNextCDN(index + 1);
                }
            };
            
            script.onerror = function(error) {
                clearTimeout(timeoutId);
                console.error(`源 ${index + 1} 加载失败:`, error);
                loadingInProgress = false;
                loadFromNextCDN(index + 1);
            };
            
            document.head.appendChild(script);
        }
        
        // 页面加载完成后开始加载库
        // 检查并显示初始化状态
        function checkInitStatus() {
            const statusIndicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            const startScanBtn = document.getElementById('start-scan');
            
            if (libraryLoaded) {
                statusIndicator.className = 'status-indicator status-pending';
                statusText.textContent = '就绪';
                startScanBtn.disabled = false;
                return true;
            } else {
                statusIndicator.className = 'status-indicator';
                statusIndicator.style.backgroundColor = '#dc3545';
                statusText.textContent = '初始化中...';
                startScanBtn.disabled = true;
                return false;
            }
        }
        
        window.addEventListener('DOMContentLoaded', function() {
            // 显示初始加载状态
            const statusText = document.getElementById('status-text');
            if (statusText) {
                statusText.textContent = '正在加载...';
            }
            
            // 开始加载库
            loadFromNextCDN(0);
            
            // 设置一个检查点，确保库最终加载成功
            setTimeout(function() {
                if (!libraryLoaded) {
                    console.warn('库加载检查点：本地文件加载失败，尝试从CDN加载...');
                    // 如果本地文件已经尝试过（index > 0），则不再重复尝试加载
                    if (!loadingInProgress) {
                        loadingInProgress = false;
                        // 从第二个源（第一个CDN）开始尝试
                        loadFromNextCDN(1);
                    }
                    
                    // 再次设置一个最终检查点
                    setTimeout(function() {
                        if (!checkInitStatus()) {
                            console.error('最终检查：扫描库仍未成功加载');
                            showAlert('初始化失败', '扫描器库无法加载。<br><br>请尝试刷新页面。', true);
                        }
                    }, 2000); // 减少到2秒进行最终检查
                } else {
                    checkInitStatus();
                }
            }, 1000); // 减少到1秒后检查
        });
    </script>
    <script>
        // 检查HTML5Qrcode库是否正确加载
        function isHtml5QrcodeSupported() {
            try {
                // 简化检查过程，只检查关键类是否存在
                return typeof Html5Qrcode !== 'undefined';
            } catch (error) {
                console.error('检查HTML5Qrcode库时出错:', error);
                return false;
            }
        }
        // DOM 元素
        const startScanBtn = document.getElementById('start-scan');
        const stopScanBtn = document.getElementById('stop-scan');
        const manualForm = document.getElementById('manual-form');
        const productInfoCard = document.getElementById('product-info');
        const weightForm = document.getElementById('weight-form');
        const videoElement = document.getElementById('video');
        const scannerOverlay = document.getElementById('scanner-overlay');
        const scanRegion = document.querySelector('.scan-region');
        const statusIndicator = document.getElementById('status-indicator');
        const statusText = document.getElementById('status-text');
        const loadingOverlay = document.getElementById('loading-overlay');
        
        // 弹窗相关
        const alertModal = new bootstrap.Modal(document.getElementById('alert-modal'));
        const alertTitle = document.getElementById('alert-title');
        const alertMessage = document.getElementById('alert-message');
        
        // 扫码相关变量
        let html5QrCode;
        let scanning = false;
        
        // 显示加载中
        function showLoading() {
            loadingOverlay.style.visibility = 'visible';
            loadingOverlay.style.opacity = '1';
        }
        
        // 隐藏加载中
        function hideLoading() {
            loadingOverlay.style.visibility = 'hidden';
            loadingOverlay.style.opacity = '0';
        }
        
        // 显示提示弹窗
        function showAlert(title, message, isError = false) {
            alertTitle.textContent = title || '提示';
            alertMessage.innerHTML = message;
            
            if (isError) {
                alertTitle.classList.add('text-danger');
            } else {
                alertTitle.classList.remove('text-danger');
            }
            
            alertModal.show();
        }
        
        // 更新摄像头状态
        function updateCameraStatus(status) {
            switch(status) {
                case 'inactive':
                    statusIndicator.className = 'status-indicator status-pending';
                    statusText.textContent = '未启动';
                    break;
                case 'requesting':
                    statusIndicator.className = 'status-indicator status-pending';
                    statusText.textContent = '请求权限中';
                    break;
                case 'active':
                    statusIndicator.className = 'status-indicator status-success';
                    statusText.textContent = '扫描中';
                    break;
                case 'error':
                    statusIndicator.className = 'status-indicator';
                    statusIndicator.style.backgroundColor = '#dc3545';
                    statusText.textContent = '访问失败';
                    break;
            }
        }
        
        // 开始扫描
        startScanBtn.addEventListener('click', function() {
            // 更新UI状态
            startScanBtn.disabled = true;
            startScanBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 正在启动摄像头...';
            updateCameraStatus('requesting');
            
            // 减少启动超时时间
            const cameraStartTimeout = setTimeout(function() {
                if (!scanning) {
                    console.log('摄像头启动超时');
                    startScanBtn.innerHTML = '<i class="bi bi-camera-video me-1"></i> 启动摄像头';
                    startScanBtn.disabled = false;
                    updateCameraStatus('error');
                    showAlert('启动超时', '摄像头启动超时，请检查浏览器是否已授予摄像头权限，或尝试刷新页面后重试。', true);
                }
            }, 8000); // 减少到8秒超时
            
            // 添加调试信息
            console.log('摄像头启动按钮被点击');
            
            // 检查HTML5Qrcode库是否正确加载
            if (!isHtml5QrcodeSupported()) {
                console.error('HTML5Qrcode库未正确加载');
                
                // 显示加载中状态
                showLoading();
                
                // 尝试重新加载库
                console.log('尝试重新加载HTML5Qrcode库...');
                
                // 重置加载状态
                libraryLoaded = false;
                loadingInProgress = false;
                
                // 直接尝试加载本地文件，提高成功率
                console.log('直接尝试加载本地文件...');
                loadFromNextCDN(cdnSources.length - 1);
                
                // 设置一个检查点，等待库加载
                let checkAttempts = 0;
                const maxAttempts = 10;
                const checkInterval = setInterval(function() {
                    checkAttempts++;
                    console.log(`检查库加载状态 (${checkAttempts}/${maxAttempts})...`);
                    
                    if (isHtml5QrcodeSupported()) {
                        clearInterval(checkInterval);
                        hideLoading();
                        console.log('库已成功加载，重新尝试启动摄像头');
                        // 重新尝试启动摄像头
                        startScanBtn.click();
                        return;
                    }
                    
                    if (checkAttempts >= maxAttempts) {
                        clearInterval(checkInterval);
                        hideLoading();
                        console.error('多次尝试后库仍未加载成功');
                        showAlert('初始化失败', '扫描器库未正确加载。<br><br>请尝试以下解决方案：<br>1. 刷新页面后重试<br>2. 清除浏览器缓存后重试<br>3. 尝试使用Chrome或Firefox等现代浏览器<br>4. 检查网络连接是否正常<br>5. 如果问题持续存在，请联系系统管理员', true);
                        startScanBtn.innerHTML = '<i class="bi bi-camera-video me-1"></i> 启动摄像头';
                        startScanBtn.disabled = false;
                        updateCameraStatus('error');
                    }
                }, 1000);
                
                return;
            }
            
            // 初始化扫描器
            if (!html5QrCode) {
                try {
                    console.log('正在初始化HTML5QrCode...');
                    html5QrCode = new Html5Qrcode("scanner-preview");
                    console.log('HTML5QrCode初始化成功');
                } catch (error) {
                    console.error('初始化扫描器失败:', error);
                    showAlert('初始化失败', `扫描器初始化失败: ${error.message}<br><br>请确保您的浏览器支持摄像头访问，并已授予摄像头权限。`, true);
                    startScanBtn.innerHTML = '<i class="bi bi-camera-video me-1"></i> 启动摄像头';
                    startScanBtn.disabled = false;
                    updateCameraStatus('error');
                    return;
                }
            }
            
            // 获取摄像头列表
            console.log('正在获取摄像头...');
            try {
                // 直接初始化摄像头，不等待设备列表
                // 配置选项 - 优化性能
                const config = {
                    fps: 10, // 降低帧率以提高性能
                    qrbox: { width: 200, height: 200 },
                    aspectRatio: 1.0,
                    formatsToSupport: [ Html5QrcodeSupportedFormats.QR_CODE ],
                    videoConstraints: {
                        facingMode: "environment",
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                };
                
                // 如果在微信环境中，需要特殊处理
                html5QrCode.start(
                    { facingMode: "environment" },
                    config,
                    (decodedText) => {
                        // 成功扫描到二维码处理逻辑
                        console.log('扫描成功:', decodedText);
                        
                        // 停止扫描
                        html5QrCode.stop().then(() => {
                            // 更新UI状态
                            videoElement.style.display = 'none';
                            scannerOverlay.style.display = 'flex';
                            scannerOverlay.innerHTML = '<i class="bi bi-check-circle" style="font-size: 2rem;"></i><p class="mt-2">扫描成功，正在查询产品信息...</p>';
                            scanRegion.style.display = 'none';
                            startScanBtn.disabled = false;
                            startScanBtn.innerHTML = '<i class="bi bi-camera-video me-1"></i> 重新扫描';
                            stopScanBtn.disabled = true;
                            scanning = false;
                            updateCameraStatus('inactive');
                            
                            // 查询产品信息
                            fetchProductInfo(decodedText);
                        }).catch(stopErr => {
                            console.error('停止扫描失败:', stopErr);
                            // 即使停止失败，也尝试查询产品信息
                            fetchProductInfo(decodedText);
                        });
                    },
                    (errorMessage) => {
                        // 扫描过程中的错误可以忽略，但记录到控制台
                        console.log('扫描过程中:', errorMessage);
                    })
                .then(() => {
                    console.log('摄像头启动成功');
                    // 更新UI状态
                    videoElement.style.display = 'block';
                    scannerOverlay.style.display = 'none';
                    scanRegion.style.display = 'block';
                    startScanBtn.innerHTML = '<i class="bi bi-camera-video me-1"></i> 重新扫描';
                    startScanBtn.disabled = false;
                    stopScanBtn.disabled = false;
                    scanning = true;
                    updateCameraStatus('active');
                })
                .catch((err) => {
                    console.error('摄像头启动失败:', err);
                    let errorMessage = `无法启动摄像头: ${err}`;
                    let troubleshootingTips = `<br><br>可能原因:<br>
                        1. 浏览器不支持摄像头访问<br>
                        2. 您拒绝了摄像头访问权限<br>
                        3. 摄像头被其他应用占用<br><br>
                        <b>解决方法:</b><br>
                        1. 请确保您使用的是现代浏览器（Chrome、Firefox、Edge等）<br>
                        2. 检查浏览器地址栏是否有摄像头权限提示<br>
                        3. 关闭可能正在使用摄像头的其他应用`;
                    
                    showAlert('摄像头访问失败', errorMessage + troubleshootingTips, true);
                    startScanBtn.innerHTML = '<i class="bi bi-camera-video me-1"></i> 启动摄像头';
                    startScanBtn.disabled = false;
                    updateCameraStatus('error');
                });
            } catch (initError) {
                console.error('初始化摄像头过程中发生异常:', initError);
                showAlert('系统错误', `初始化摄像头过程中发生异常: ${initError.message}<br><br>请刷新页面后重试。`, true);
                startScanBtn.innerHTML = '<i class="bi bi-camera-video me-1"></i> 启动摄像头';
                startScanBtn.disabled = false;
                updateCameraStatus('error');
            }
        });
        
        // 停止扫描
        stopScanBtn.addEventListener('click', function() {
            if (html5QrCode && scanning) {
                html5QrCode.stop().then(() => {
                    // 更新UI状态
                    videoElement.style.display = 'none';
                    scannerOverlay.style.display = 'flex';
                    scannerOverlay.innerHTML = '<i class="bi bi-camera-video-off" style="font-size: 2rem;"></i><p class="mt-2">点击下方按钮启动摄像头</p>';
                    scanRegion.style.display = 'none';
                    startScanBtn.disabled = false;
                    startScanBtn.innerHTML = '<i class="bi bi-camera-video me-1"></i> 启动摄像头';
                    stopScanBtn.disabled = true;
                    scanning = false;
                    updateCameraStatus('inactive');
                    console.log('摄像头已停止');
                }).catch(err => {
                    console.error('停止扫描失败:', err);
                    showAlert('停止失败', `停止扫描失败: ${err}`, true);
                });
            }
        });
        
        // 手动输入表单提交
        manualForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('手动输入表单提交');
            const productId = document.getElementById('manual-product-id').value.trim();
            if (productId) {
                console.log('准备查询产品ID:', productId);
                fetchProductInfo(productId);
            } else {
                console.log('产品ID为空');
                showAlert('输入错误', '请输入有效的产品编号');
            }
        });
        
        // 添加点击事件监听器作为备用方案
        document.querySelector('#manual-form button').addEventListener('click', function(e) {
            if (!e.target.form.reportValidity()) return;
            e.preventDefault();
            console.log('手动输入查询按钮点击');
            const productId = document.getElementById('manual-product-id').value.trim();
            if (productId) {
                console.log('通过按钮点击查询产品ID:', productId);
                fetchProductInfo(productId);
            } else {
                console.log('产品ID为空');
                showAlert('输入错误', '请输入有效的产品编号');
            }
        });
        
        // 重量表单提交
        weightForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const productId = document.getElementById('product-id-input').value;
            const coilWeight = document.getElementById('coil-weight').value;
            const finalWeight = document.getElementById('final-weight').value;
            
            if (!productId) {
                showAlert('提交失败', '产品编号不能为空', true);
                return;
            }
            
            showLoading();
            
            const formData = new FormData();
            formData.append('product_id', productId);
            if (coilWeight) formData.append('coil_weight', coilWeight);
            if (finalWeight) formData.append('final_weight', finalWeight);
            
            fetch('/api/update_weight', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert('更新成功', '产品重量信息已成功更新');
                    // 刷新产品信息
                    fetchProductInfo(productId);
                } else {
                    showAlert('更新失败', data.message, true);
                }
            })
            .catch(error => {
                hideLoading();
                console.error('更新错误:', error);
                showAlert('系统错误', '更新过程中发生错误: ' + error.message, true);
            });
        });
        
        // 获取产品信息
        function fetchProductInfo(productId) {
            console.log('正在查询产品ID:', productId);
            showLoading();
            
            // 验证产品ID
            if (!productId || productId.trim() === '') {
                hideLoading();
                console.error('产品ID无效:', productId);
                showAlert('输入错误', '产品编号不能为空', true);
                return;
            }
            
            const formData = new FormData();
            formData.append('product_id', productId.trim());
            
            console.log('发送API请求，产品ID:', productId.trim());
            
            fetch('/api/find_product', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('API响应状态:', response.status);
                if (!response.ok) {
                    throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                hideLoading();
                console.log('API返回数据:', data);
                if (data.success) {
                    console.log('查询成功，显示产品信息');
                    displayProductInfo(data.product);
                    // 清空手动输入框
                    document.getElementById('manual-product-id').value = '';
                } else {
                    console.error('查询失败:', data.message);
                    showAlert('查询失败', data.message || '未知错误', true);
                }
            })
            .catch(error => {
                hideLoading();
                console.error('查询错误详情:', error);
                showAlert('系统错误', '查询过程中发生错误: ' + error.message, true);
            });
        }
        
        // 显示产品信息
        function displayProductInfo(product) {
            // 添加调试信息，查看API返回的产品数据
            console.log('产品信息:', product);
            console.log('开始更新产品信息显示');
            
            document.getElementById('product-uid').textContent = product.uid || '无';
            document.getElementById('product-code').textContent = product.product_code || '无';
            document.getElementById('product-spec').textContent = product.spec_info || '无';
            document.getElementById('product-req').textContent = product.special_req || '无';
            document.getElementById('product-customer').textContent = product.customer || '无';
            document.getElementById('product-time').textContent = product.update_time || '无';
            
            // 设置隐藏的产品ID输入框
            document.getElementById('product-id-input').value = product.uid;
            
            // 预填充重量输入框
            if (product.coil_weight) {
                document.getElementById('coil-weight').value = product.coil_weight;
            } else {
                document.getElementById('coil-weight').value = '';
            }
            
            if (product.final_weight) {
                document.getElementById('final-weight').value = product.final_weight;
            } else {
                document.getElementById('final-weight').value = '';
            }
            
            // 显示产品信息卡片 - 确保它是可见的
            console.log('显示产品信息卡片，当前display状态:', productInfoCard.style.display);
            productInfoCard.style.display = 'block';
            productInfoCard.style.visibility = 'visible';
            productInfoCard.style.opacity = '1';
            console.log('产品信息卡片已设置为可见');
            
            // 滚动到产品信息卡片
            productInfoCard.scrollIntoView({ behavior: 'smooth' });
            
            // 添加一个延迟检查，确保产品信息卡片确实显示了
            setTimeout(() => {
                console.log('延迟检查产品信息卡片显示状态:', 
                    productInfoCard.style.display,
                    window.getComputedStyle(productInfoCard).display,
                    '可见性:', productInfoCard.style.visibility,
                    '透明度:', productInfoCard.style.opacity);
                
                // 如果仍然没有显示，尝试再次强制显示
                if (window.getComputedStyle(productInfoCard).display === 'none') {
                    console.log('产品信息卡片仍未显示，尝试强制显示');
                    productInfoCard.style.cssText = 'display: block !important; visibility: visible !important; opacity: 1 !important;';
                }
            }, 500);
        }
    </script>
<script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
<script>
    wx.config({
        debug: false,
        appId: 'YOUR_APP_ID',
        timestamp: YOUR_TIMESTAMP,
        nonceStr: 'YOUR_NONCE_STR',
        signature: 'YOUR_SIGNATURE',
        jsApiList: ['scanQRCode']
    });

    wx.ready(function() {
        document.getElementById('start-scan').onclick = function() {
            wx.scanQRCode({
                needResult: 1,
                scanType: ['qrCode', 'barCode'],
                success: function(res) {
                    var result = res.resultStr;
                    // 处理扫描结果
                    console.log('扫描结果:', result);
                }
            });
        };
    });
</script>
</body>
</html>