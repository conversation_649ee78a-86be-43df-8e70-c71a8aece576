<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Html5QrCode库测试 - 称重系统</title>
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .test-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .test-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-code-square me-2"></i>Html5QrCode库测试</h2>
                    <a href="/scan" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left me-1"></i>返回扫描页面
                    </a>
                </div>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-check-circle me-2"></i>库加载测试</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-results">
                            <div class="d-flex justify-content-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">测试中...</span>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button id="retest-btn" class="btn btn-primary" disabled>
                                <i class="bi bi-arrow-clockwise me-1"></i>重新测试
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-terminal me-2"></i>测试日志</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-log" class="log-output"></div>
                        <div class="mt-3">
                            <button id="clear-log-btn" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-trash me-1"></i>清空日志
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 库信息 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-info-circle me-2"></i>库信息</h5>
                    </div>
                    <div class="card-body">
                        <div id="library-info">
                            <p>等待库加载...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    
    <!-- 加载Html5QrCode库 -->
    <script src="{{ url_for('static', filename='js/html5-qrcode.min.js') }}"></script>
    
    <script>
        const testResults = document.getElementById('test-results');
        const testLog = document.getElementById('test-log');
        const libraryInfo = document.getElementById('library-info');
        const retestBtn = document.getElementById('retest-btn');
        const clearLogBtn = document.getElementById('clear-log-btn');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'text-danger' : type === 'warning' ? 'text-warning' : 'text-success';
            testLog.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            testLog.scrollTop = testLog.scrollHeight;
            console.log(message);
        }
        
        function addTestResult(title, status, message) {
            const resultClass = status === 'success' ? 'test-success' : status === 'error' ? 'test-error' : 'test-warning';
            const icon = status === 'success' ? 'check-circle' : status === 'error' ? 'x-circle' : 'exclamation-triangle';
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${resultClass}`;
            resultDiv.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="bi bi-${icon} me-2"></i>
                    <strong>${title}</strong>
                </div>
                <div class="mt-1">${message}</div>
            `;
            
            return resultDiv;
        }
        
        function runTests() {
            testResults.innerHTML = '';
            log('🚀 开始Html5QrCode库测试');
            
            // 测试1: 检查全局变量
            log('📋 测试1: 检查全局变量');
            if (typeof Html5Qrcode !== 'undefined') {
                testResults.appendChild(addTestResult('全局Html5Qrcode', 'success', '全局Html5Qrcode变量已定义'));
                log('✅ 全局Html5Qrcode变量已定义');
            } else {
                testResults.appendChild(addTestResult('全局Html5Qrcode', 'error', '全局Html5Qrcode变量未定义'));
                log('❌ 全局Html5Qrcode变量未定义', 'error');
            }
            
            // 测试2: 检查window对象
            log('📋 测试2: 检查window对象');
            if (typeof window.Html5Qrcode !== 'undefined') {
                testResults.appendChild(addTestResult('window.Html5Qrcode', 'success', 'window.Html5Qrcode已定义'));
                log('✅ window.Html5Qrcode已定义');
            } else {
                testResults.appendChild(addTestResult('window.Html5Qrcode', 'warning', 'window.Html5Qrcode未定义'));
                log('⚠️ window.Html5Qrcode未定义', 'warning');
            }
            
            // 测试3: 检查库对象
            log('📋 测试3: 检查库对象');
            if (typeof window.__Html5QrcodeLibrary__ !== 'undefined') {
                testResults.appendChild(addTestResult('__Html5QrcodeLibrary__', 'success', '库对象已定义'));
                log('✅ __Html5QrcodeLibrary__已定义');
                
                // 显示库对象内容
                const lib = window.__Html5QrcodeLibrary__;
                log(`📦 库对象包含: ${Object.keys(lib).join(', ')}`);
                
                if (lib.Html5Qrcode) {
                    testResults.appendChild(addTestResult('Html5Qrcode类', 'success', '在库对象中找到Html5Qrcode类'));
                    log('✅ 在库对象中找到Html5Qrcode类');
                    
                    // 设置全局引用
                    if (typeof Html5Qrcode === 'undefined') {
                        window.Html5Qrcode = lib.Html5Qrcode;
                        window.Html5QrcodeSupportedFormats = lib.Html5QrcodeSupportedFormats;
                        window.Html5QrcodeScanType = lib.Html5QrcodeScanType;
                        log('🔧 已设置全局引用');
                    }
                }
            } else {
                testResults.appendChild(addTestResult('__Html5QrcodeLibrary__', 'error', '库对象未定义'));
                log('❌ __Html5QrcodeLibrary__未定义', 'error');
            }
            
            // 测试4: 尝试创建实例
            log('📋 测试4: 尝试创建实例');
            try {
                if (typeof Html5Qrcode !== 'undefined') {
                    // 创建一个临时div用于测试
                    const testDiv = document.createElement('div');
                    testDiv.id = 'test-qr-reader';
                    testDiv.style.display = 'none';
                    document.body.appendChild(testDiv);
                    
                    const testInstance = new Html5Qrcode('test-qr-reader');
                    testResults.appendChild(addTestResult('实例创建', 'success', '成功创建Html5Qrcode实例'));
                    log('✅ 成功创建Html5Qrcode实例');
                    
                    // 清理
                    document.body.removeChild(testDiv);
                } else {
                    testResults.appendChild(addTestResult('实例创建', 'error', 'Html5Qrcode类不可用'));
                    log('❌ Html5Qrcode类不可用', 'error');
                }
            } catch (error) {
                testResults.appendChild(addTestResult('实例创建', 'error', `创建实例失败: ${error.message}`));
                log(`❌ 创建实例失败: ${error.message}`, 'error');
            }
            
            // 更新库信息
            updateLibraryInfo();
            
            retestBtn.disabled = false;
            log('🏁 测试完成');
        }
        
        function updateLibraryInfo() {
            let info = '<h6>库状态信息:</h6><ul>';
            
            info += `<li><strong>Html5Qrcode类型:</strong> ${typeof Html5Qrcode}</li>`;
            info += `<li><strong>window.Html5Qrcode:</strong> ${typeof window.Html5Qrcode}</li>`;
            info += `<li><strong>__Html5QrcodeLibrary__:</strong> ${typeof window.__Html5QrcodeLibrary__}</li>`;
            
            if (typeof window.__Html5QrcodeLibrary__ !== 'undefined') {
                const lib = window.__Html5QrcodeLibrary__;
                info += `<li><strong>库对象键:</strong> ${Object.keys(lib).join(', ')}</li>`;
            }
            
            info += `<li><strong>脚本标签数量:</strong> ${document.querySelectorAll('script[src*="html5-qrcode"]').length}</li>`;
            info += `<li><strong>用户代理:</strong> ${navigator.userAgent.substring(0, 100)}...</li>`;
            
            info += '</ul>';
            libraryInfo.innerHTML = info;
        }
        
        // 事件监听
        retestBtn.addEventListener('click', runTests);
        clearLogBtn.addEventListener('click', () => {
            testLog.innerHTML = '';
        });
        
        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', () => {
            // 延迟一下确保库完全加载
            setTimeout(runTests, 1000);
        });
    </script>
</body>
</html>
