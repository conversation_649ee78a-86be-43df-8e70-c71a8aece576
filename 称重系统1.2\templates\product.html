<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品详情 - 称重管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap-icons.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">称重管理系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/scan">扫码称重</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin">管理后台</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>产品详情</h2>
                    <a href="/scan" class="btn btn-secondary">返回扫码页面</a>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>产品信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">产品编号：</div>
                            <div class="col-md-8">{{ product.uid }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">产品代码：</div>
                            <div class="col-md-8">{{ product.product_code }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">规格信息：</div>
                            <div class="col-md-8">{{ product.spec_info }}</div>
                        </div>
                        {% if product.special_req %}
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">特殊要求：</div>
                            <div class="col-md-8">{{ product.special_req }}</div>
                        </div>
                        {% endif %}
                        {% if product.customer %}
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">客户信息：</div>
                            <div class="col-md-8">{{ product.customer }}</div>
                        </div>
                        {% endif %}
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">更新时间：</div>
                            <div class="col-md-8">{{ product.update_time }}</div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-speedometer2 me-2"></i>重量信息</h5>
                    </div>
                    <div class="card-body">
                        <form id="weight-form">
                            <input type="hidden" id="product_id" name="product_id" value="{{ product.uid }}">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="coil_weight" class="form-label fw-bold">卷重(kg)：</label>
                                </div>
                                <div class="col-md-8">
                                    <input type="number" class="form-control" id="coil_weight" name="coil_weight" step="0.01" value="{{ product.coil_weight }}">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="final_weight" class="form-label fw-bold">成品重量(kg)：</label>
                                </div>
                                <div class="col-md-8">
                                    <input type="number" class="form-control" id="final_weight" name="final_weight" step="0.01" value="{{ product.final_weight }}">
                                </div>
                            </div>
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">保存重量信息</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const weightForm = document.getElementById('weight-form');
            
            weightForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(weightForm);
                
                fetch('/api/update_weight', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('重量更新成功！');
                    } else {
                        alert('错误：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('提交失败，请重试！');
                });
            });
        });
    </script>
</body>
</html>